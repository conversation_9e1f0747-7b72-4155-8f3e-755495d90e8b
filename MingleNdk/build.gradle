apply plugin: 'com.android.library'

android {
    namespace 'com.mingle.ndk'
    ndkVersion Versions.ndkVersion
    compileSdk Versions.compileSdkVersion
    buildToolsVersion = Versions.buildToolsVersion

    defaultConfig {
        minSdkVersion Versions.minSdkVersion
        targetSdkVersion Versions.targetSdkVersion

        ndk {
            abiFilters 'x86', 'x86_64', 'armeabi-v7a', 'arm64-v8a'
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    externalNativeBuild {
        cmake {
            path 'src/main/CMakeLists.txt'
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['native-libs']
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
}

dependencies {
    implementation("com.getkeepsafe.relinker:relinker:${Versions.reLinker}")
}
