import java.io.FileInputStream
import java.util.*

val booleanType = "Boolean"
val stringType = "String"

repositories {
    maven("https://s3.amazonaws.com/repo.commonsware.com")
    maven("https://clojars.org/repo")
    maven {
        setUrl("https://jitpack.io")
    }
    maven("https://artifactory.appodeal.com/appodeal")
}

plugins {
    id("com.android.application")
    kotlin("android")
    kotlin("kapt")
    id("com.google.devtools.ksp")
    id("com.google.firebase.crashlytics")
    id("com.google.gms.google-services")
}

kapt {
    correctErrorTypes = true
}

android {
    namespace = "com.mingle.twine"
    ndkVersion = Versions.ndkVersion
    compileSdk = Versions.compileSdkVersion
    buildToolsVersion = Versions.buildToolsVersion

    buildFeatures {
        dataBinding = true
        viewBinding = true
        buildConfig = true
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        isCoreLibraryDesugaringEnabled = true
    }

    packaging {
        resources {
            excludes += setOf("META-INF/DEPENDENCIES.txt", "META-INF/LICENSE.txt", "META-INF/NOTICE.txt", "META-INF/NOTICE", "META-INF/LICENSE", "META-INF/DEPENDENCIES", "META-INF/notice.txt", "META-INF/license.txt", "META-INF/dependencies.txt", "META-INF/LGPL2.1", "META-INF/rxjava.properties", "publicsuffixes.gz")
        }
    }

    bundle(Action {
        language(Action {
            enableSplit = false
        })
        density(Action {
            enableSplit = true
        })
        abi(Action {
            enableSplit = true
        })
    })

    flavorDimensions += listOf("default")
    defaultConfig {
        minSdk = Versions.minSdkVersion
        targetSdk = Versions.targetSdkVersion
        multiDexEnabled = true
        vectorDrawables.useSupportLibrary = true

        ndk {
            abiFilters += listOf("x86", "x86_64", "armeabi-v7a", "arm64-v8a")
        }

        buildConfigField(booleanType, "IS_STAGING", project.properties["IS_STAGING"] as String)
        buildConfigField(booleanType, "SHOWING_ID", "false")

        buildConfigField(stringType, "STAGING_SERVER_ADDRESS", "\"https://api.jsh-dev.com\"")
        buildConfigField(stringType, "PROD_SERVER_ADDRESS", "\"https://api.justsayhi.com\"")

        buildConfigField(stringType, "S3_ADDRESS", "\"https://jsh-staging.s3.amazonaws.com\"")
        buildConfigField(stringType, "S3_BUCKET", "\"jsh-staging\"")

        buildConfigField(stringType, "S3_ADDRESS_REAL_STAGING", "\"https://jsh-real-staging.s3.amazonaws.com\"")
        buildConfigField(stringType, "S3_BUCKET_REAL_STAGING", "\"jsh-real-staging\"")

        buildConfigField(stringType, "S3_CDN_ADDRESS", "\"https://cdn-v2.justsayhi.com\"")
        buildConfigField(stringType, "S3_STAGING_CDN_ADDRESS", "\"https://jsh-real-staging.s3.amazonaws.com\"")

        buildConfigField(stringType, "INBOX_SERVER_ADDRESS", "\"https://inbox.justsayhi.com\"")
        buildConfigField(stringType, "INBOX_STAGING_SERVER_ADDRESS", "\"https://inbox.jsh-dev.com\"")

        buildConfigField(stringType, "TERM_OF_SERVICE_URL", "\"/info/%s/term_of_service\"")
        buildConfigField(stringType, "CODE_OF_CONDUCT_URL", "\"/info/%s/code_of_conduct\"")

        buildConfigField(booleanType, "IS_TRACKING_EVENT", "true")
    }

    val signingFile = project.file("signing.properties")
    if (signingFile.exists()) {
        val properties = Properties()
        properties.load(FileInputStream(signingFile))
        signingConfigs {
            create("dateinasia") {
                storeFile = project.file(properties["DATEINASIA_STORE_FILE"]!!)
                storePassword = properties["STORE_PASSWORD"] as String?
                keyAlias = properties["DATEINASIA_KEY_ALIAS"] as String?
                keyPassword = properties["KEY_PASSWORD"] as String?
            }
        }
    }

    buildTypes {
        getByName("release") {
            isShrinkResources = true
            isMinifyEnabled = true
            proguardFiles(getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro")
            signingConfig = signingConfigs.getByName("dateinasia")
        }
    }

    productFlavors {
        create("dateinasia") {
            dimension = "default"
            applicationId = "com.mingle.dateinasia"
            versionCode = 270
            versionName = "7.20.0"
            manifestPlaceholders["admob_id"] = "ca-app-pub-4975204441968927~6925990927"
            buildConfigField(booleanType, "IS_STAGING", "false")
            buildConfigField(stringType, "APP_NAME", "\"dateinasia\"")
            buildConfigField(stringType, "CHANNEL", "\"dateinasia\"")
            buildConfigField(stringType, "BASE64_ENCODED_PUBLICKEY", "\"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjy1f+nECb7ALmvEfkD+oVxcj8R1PmUK95Lh+eXZLYpvxf4nLIAVKKq+J5l/8Z9bGrlZWwULEzbqdE4466XFqIaY/hXj73aXNJ9XufQbikRYOj6LUvrfyZDNK7vx2weFxASU8PbGgE0balBDh9LMROIbWsYNUPdI+40Va1dTW8Zkg5WpG7sGm+hR9TE2M8OOzRVco1QDkK9xr30HSFubi8+/0aoJkdrbRIQrIR/InB8XkSVKZeSFeXFPbVgB7vToXKg8/2Tz+SAqRm+7EiDoUt/sevMGbp3gUjOQmDNNtiyrjdPylQdVMZSV6noUPRP/stasji5OQ65Kgai+CYBLXxQIDAQAB\"")
            buildConfigField(stringType, "APP_SENDER_ID", "\"841104978084\"")
            buildConfigField(stringType, "CHECK_VERSION_URL", "\"https://s3-ap-southeast-1.amazonaws.com/jsh-staging/version/dateinasia_version.jsh\"")
            buildConfigField(booleanType, "IS_TRACKING_TEST", "false")
            buildConfigField(booleanType, "IS_INAPP_UPDATE_TEST", "false")
            buildConfigField(stringType, "GIPHY_KEY", "\"CjN/IGl7J3ljCgsVTzFbW0IKFyUvaFxqRgh/anIkUXc=\"")
            buildConfigField(stringType, "POLLFISH_KEY", "\"AVMIVglRAQMYDwpWDk5WCVcDTwNRBwAeUQQAXQFQAwMIVA4G\"")
            buildConfigField(stringType, "PRIVACY_URL", "\"https://dateinasia.innovatedating.com/privacy?language_preference=%s\"")
            buildConfigField(stringType, "SAFETY_HUB_URL", "\"https://dateinasia.innovatedating.com/dating_and_safety_hub?language_preference=%s\"")

            buildConfigField(stringType, "STORAGE_DIR_NAME", "\"DateInAsia\"")
        }
    }

    sourceSets {
        getByName("main") {
            res.setSrcDirs(mutableSetOf("src/main/res", "src/main/res_inbox",
                    "src/main/res_localization"))
            assets.setSrcDirs(mutableSetOf("src/main/assets"))
        }
        getByName("dateinasia") {
            res.setSrcDirs(mutableSetOf("src/dateinasia/res"))
            assets.setSrcDirs(mutableSetOf("src/dateinasia/assets"))
        }
    }

    androidResources {
        noCompress += listOf("tflite")
    }

    lint {
        abortOnError = false
        checkReleaseBuilds = false
    }
}

dependencies {
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.1.5")

    implementation(project(":MingleGlobal"))
    implementation(project(":MingleInbox"))
    implementation(project(":MingleSticker"))

    implementation(Libs.supportLibs)
    implementation(Libs.glideLib)
    implementation(Libs.daggerLib)
    ksp(Libs.annotationProcessorLibs)

    implementation(platform("com.google.firebase:firebase-bom:${Versions.firebaseBomVersion}"))
    implementation(Libs.firebaseLibs)

    implementation(project(":AsymmetricGridviewLibrary"))
    implementation(project(":LeonidsLibrary"))
    implementation(project(":placeholderview"))
    implementation(project(":placeholderview-compiler"))

//    debugImplementation(Deps.onlyDebug)
//    releaseImplementation(Deps.onlyRelease)
    implementation("com.google.android.flexbox:flexbox:${Versions.flexbox}")
    implementation("de.hdodenhof:circleimageview:${Versions.circleimageview}")
    implementation("com.otaliastudios:cameraview:${Versions.cameraView}")
    implementation("com.pusher:pusher-java-client:${Versions.pusherVersion}")
    implementation("io.branch.sdk.android:library:${Versions.branchIO}")

    implementation(group = "cz.msebera.android", name = "httpclient", version = Versions.httpclient)
    implementation("se.simbio.encryption:library:${Versions.simbioEncryption}")
    implementation("info.hoang8f:android-segmented:${Versions.segmented}")
    implementation("com.neovisionaries:nv-i18n:${Versions.nvi18n}")
    implementation("com.android.billingclient:billing:${Versions.billing}")
    implementation("net.qiujuer.genius:blur:${Versions.geniusBlur}")

    implementation(project(":toro-core"))
    implementation(project(":toro-exoplayer"))
    implementation("com.google.android.exoplayer:exoplayer:${Versions.exoplayer}")
    implementation("com.google.android.exoplayer:exoplayer-core:${Versions.exoplayer}")
    implementation("com.google.android.exoplayer:extension-okhttp:${Versions.exoplayer}")

    implementation("com.romandanylyk:pageindicatorview:${Versions.pageindicatorview}@aar")
    implementation("me.leolin:ShortcutBadger:${Versions.shortcutBadger}@aar")
    implementation("com.github.takusemba:spotlight:${Versions.spotlight}")
    implementation("com.theartofdev.edmodo:android-image-cropper:${Versions.imageCropper}")

    ksp("androidx.room:room-compiler:${Versions.roomVersion}")

    implementation("com.appodeal.ads:sdk:${Versions.appodeal}") {
        exclude(module = "gson")
        exclude(module = "okhttp")
        exclude(module = "retrofit")
        exclude(module = "okio")
        exclude(module = "logging-interceptor")
        exclude(module = "converter-gson")
        exclude(group = "com.google.android.exoplayer", module = "exoplayer-core")
        exclude(group = "com.appodeal.ads.sdk.services", module = "stack_analytics")

        //https://docs.appodeal.com/android/advanced/configure-mediated-networks
    }
    implementation("com.pollfish:pollfish-googleplay:${Versions.pollfish}")

    //epoxy
    kapt(Libs.Epoxy.processor)

    //NSFW detector
    //implementation("com.google.mlkit:image-labeling:${Versions.mlkitImageLabelingVersion}")
    implementation("com.google.mlkit:image-labeling-custom:${Versions.mlkitImageLabelingCustomVersion}")
    //implementation("com.google.android.gms:play-services-mlkit-face-detection:${Versions.mlkitFaceDetectionVersion}")
    //implementation("com.google.android.gms:play-services-mlkit-text-recognition:${Versions.mlkitTextRecognitionVersion}")

    //debugImplementation("com.squareup.leakcanary:leakcanary-android:${Versions.leakcanary}")
    //Inspection of HTTP(S) requests/responses
    debugImplementation("com.github.ChuckerTeam.Chucker:library:${Versions.chucker}")
    releaseImplementation("com.github.ChuckerTeam.Chucker:library-no-op:${Versions.chucker}")
    //Detects Android ANRs
    api("com.github.anrwatchdog:anrwatchdog:${Versions.anrwatchdog}")
    //debugImplementation("com.gu.android:toolargetool:${Versions.toolargetool}")
}

//configurations.all {
//Uncomment it whenever you want to update remote module.
//resolutionStrategy.cacheChangingModulesFor(0, TimeUnit.SECONDS)
//}