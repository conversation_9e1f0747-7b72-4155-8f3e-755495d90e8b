# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /Users/<USER>/MingleWorkspace/adt-mac/sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.kts.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

-keep class org.ocpsoft.prettytime.i18n.**

#facebook
-keep class com.facebook.** {
   *;
}

-dontwarn org.apache.log4j.**
-dontnote org.apache.log4j.**

# Class names are needed in reflection
-keepnames class com.amazonaws.**
# Request handlers defined in request.handlers
-keep class com.amazonaws.services.**.*Handler
# The following are referenced but aren't required to run
-dontwarn com.fasterxml.jackson.**
-dontwarn org.apache.commons.logging.**
# Android 6.0 release removes support for the Apache HTTP client
-dontwarn org.apache.http.**
# The SDK has several references of Apache HTTP client
-dontwarn com.amazonaws.http.**
-dontwarn com.amazonaws.metrics.**

-dontwarn com.squareup.**
-dontwarn okio.**

#eventbus
-keepattributes *Annotation*
-keepclassmembers class ** {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }

# Only required if you use AsyncExecutor
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}

##---------------Begin: proguard configuration for Retrofit  ----------
# Retrofit does reflection on generic parameters. InnerClasses is required to use Signature and
# EnclosingMethod is required to use InnerClasses.
-keepattributes Signature, InnerClasses, EnclosingMethod

# Retrofit does reflection on method and parameter annotations.
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations

# Keep annotation default values (e.g., retrofit2.http.Field.encoded).
-keepattributes AnnotationDefault

# Retain service method parameters when optimizing.
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# Ignore annotation used for build tooling.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Ignore JSR 305 annotations for embedding nullability information.
-dontwarn javax.annotation.**

# Guarded by a NoClassDefFoundError try/catch and only used when on the classpath.
-dontwarn kotlin.Unit

# Top-level functions that can only be used by Kotlin.
-dontwarn retrofit2.KotlinExtensions
-dontwarn retrofit2.KotlinExtensions$*

# With R8 full mode, it sees no subtypes of Retrofit interfaces since they are created with a Proxy
# and replaces all potential values with null. Explicitly keeping the interfaces prevents this.
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface <1>

# Keep inherited services.
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface * extends <1>

# With R8 full mode generic signatures are stripped for classes that are not
# kept. Suspend functions are wrapped in continuations where the type argument
# is used.
-keep,allowobfuscation,allowshrinking interface retrofit2.Call
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

# R8 full mode strips generic signatures from return types if not kept.
-if interface * { @retrofit2.http.* public *** *(...); }
-keep,allowoptimization,allowshrinking,allowobfuscation class <3>

# With R8 full mode generic signatures are stripped for classes that are not kept.
-keep,allowobfuscation,allowshrinking class retrofit2.Response
##---------------End: proguard configuration for Retrofit  ----------

# Application classes that will be serialized/deserialized over Gson
# -keep class mypersonalclass.data.model.** { *; }
-keep class com.mingle.twine.models.** { <fields>; }
-keep class com.mingle.twine.utils.facebook.** { <fields>; }

# Pollfish
-dontwarn com.pollfish.**
-keep class com.pollfish.** { *; }

# HttpHeaders
-dontwarn com.google.common.net.HttpHeaders

# Dagger
-dontwarn dagger.**

##---------------Begin: proguard configuration for Gson  ----------
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature
# For using GSON @Expose annotation
-keepattributes *Annotation*
# Gson specific classes
-dontwarn sun.misc.**
#-keep class com.google.gson.stream.** { *; }
# Application classes that will be serialized/deserialized over Gson
-keep class com.google.gson.examples.android.model.** { <fields>; }
# Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer
# Prevent R8 from leaving Data object members always null
-keep class * {
  @com.google.gson.annotations.SerializedName <fields>;
}
-keep class com.google.gson.reflect.TypeToken
-keep class * extends com.google.gson.reflect.TypeToken
-keep public class * implements java.lang.reflect.Type
# Retain generic signatures of TypeToken and its subclasses with R8 version 3.0 and higher.
-keep,allowobfuscation,allowshrinking class com.google.gson.reflect.TypeToken
-keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken
##---------------End: proguard configuration for Gson  ----------

# Amplitude
-keep class com.google.android.gms.ads.** { *; }
-dontwarn okio.**

# firebase craslytics
-keepattributes *Annotation*
-keepattributes SourceFile,LineNumberTable
-keep public class * extends java.lang.Exception
-printmapping mapping.txt

# cwac
-keep class com.commonsware.cwac.** { *; }
-dontwarn com.commonsware.cwac.*

-keep public class com.google.android.gms.* { public *; }
-dontwarn com.google.android.gms.**

# glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}

# bottombar
-dontwarn com.roughike.bottombar.**

# google ads id
-keep class * extends java.util.ListResourceBundle {
protected Object[][] getContents();  }

-keep public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;      }

-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
    @com.google.android.gms.common.annotation.KeepName *;
}

-keepnames class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Keep Cuebiq
-keepattributes Signature
-keepattributes Exceptions

##---------------Begin: proguard configuration for OkHttp3  ----------
# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**
# A resource is loaded with a relative path so the package of this class must be preserved.
-adaptresourcefilenames okhttp3/internal/publicsuffix/PublicSuffixDatabase.gz
# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*
# OkHttp platform used only on JVM and when Conscrypt and other security providers are available.
-dontwarn okhttp3.internal.platform.**
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**
##---------------End: proguard configuration for OkHttp3  ----------

 #GSON
-keep class com.google.gson.** { *; }

##---------------Begin: proguard configuration for Okio  ----------
-dontwarn okio.**
-keep class okio.** { *; }
##---------------End: proguard configuration for Okio  ----------

-dontwarn org.slf4j.**

-dontwarn com.digits.sdk.android.**
-keep class com.facebook.applinks.** { *; }
-keepclassmembers class com.facebook.applinks.** { *; }
-dontwarn com.google.firebase.appindexing.**

# Matisse
-dontwarn com.zhihu.matisse.engine.impl.*

# BottomNavigationView
-keep public class com.google.android.material.bottomnavigation.BottomNavigationView { *; }
-keep public class com.google.android.material.bottomnavigation.BottomNavigationMenuView { *; }
-keep public class com.google.android.material.bottomnavigation.BottomNavigationItemView { *; }

# okhttp3
-dontwarn javax.annotation.**
-dontwarn org.conscrypt.**
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase
-keep class okhttp3.internal.http2.StreamResetException
-keep class org.wysaid.nativePort.** { *; }

#In-app billing
-keep class com.android.vending.billing.**

-keep class com.mingle.twine.views.adapters.viewholder.meetcard.** { <fields>; }

-keep class com.github.anrwatchdog.** { *; }

# com.theartofdev.edmodo:android-image-cropper
-keep class androidx.appcompat.widget.** { *; }

#####---------------Begin: Appodeal  ----------
# Keep all Appodeal core SDK classes and members
-keep class com.appodeal.** { *; }
-keepclassmembers class com.appodeal.** { *; }
-dontwarn com.appodeal.**

# Keep all mediated network classes and members
-keep class com.appodeal.ads.sdk.networks.** { *; }
-keepclassmembers class com.appodeal.ads.sdk.networks.** { *; }
-dontwarn com.appodeal.ads.sdk.networks.**

# Keep common ad network packages
-keep class com.google.android.gms.ads.** { *; }
-keep class com.applovin.** { *; }
-keep class com.unity3d.ads.** { *; }
-keep class com.amazon.device.ads.** { *; }
-keep class com.facebook.ads.** { *; }
-keep class com.chartboost.** { *; }
-keep class com.ironsource.** { *; }

# Suppress warnings for common ad networks
-dontwarn com.google.android.gms.ads.**
-dontwarn com.applovin.**
-dontwarn com.unity3d.ads.**
-dontwarn com.amazon.device.ads.**
-dontwarn com.facebook.ads.**
-dontwarn com.chartboost.**
-dontwarn com.ironsource.**

# Mediation:Tapjoy (optional)
-keep class com.tapjoy.** { *; }
-keep class com.moat.** { *; }
-keepattributes JavascriptInterface
-keepattributes *Annotation*
-keep class * extends java.util.ListResourceBundle {
protected Object[][] getContents();
}
-keep public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
public static final *** NULL;
}
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
@com.google.android.gms.common.annotation.KeepName *;
}
-keepnames class * implements android.os.Parcelable {
public static final ** CREATOR;
}
-keep class com.google.android.gms.ads.identifier.** { *; }
-dontwarn com.tapjoy.**
#####---------------End: Appodeal  ----------

-dontwarn java.lang.management.**
-dontwarn com.bytedance.sdk.**
-dontwarn com.bytedance.component.sdk.**
-dontwarn com.huawei.hms.ads.**

# BranchIO https://help.branch.io/developers-hub/docs/android-basic-integration
-keep class com.huawei.hms.ads.** { *; }
-keep interface com.huawei.hms.ads.** { *; }