package com.mingle.twine.fragments.inbox

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.LinearGradient
import android.graphics.Shader
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnLayoutChangeListener
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewTreeObserver
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.Observer
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.appbar.AppBarLayout.BaseOnOffsetChangedListener
import com.mingle.global.extensions.safeLet2
import com.mingle.global.extensions.safeLet3
import com.mingle.global.extensions.safeLet5
import com.mingle.global.utils.ArrayUtil
import com.mingle.global.utils.GlideUtil
import com.mingle.global.utils.KeyboardUtil
import com.mingle.global.utils.Log
import com.mingle.global.utils.PathUtils
import com.mingle.global.utils.S3Util
import com.mingle.global.utils.ToastUtil.showToastSafe
import com.mingle.inbox.constants.Constants
import com.mingle.inbox.constants.ConversationStatus
import com.mingle.inbox.interfaces.InboxGetConversationCallback
import com.mingle.inbox.model.InboxConversation
import com.mingle.inbox.model.InboxGiphyContent
import com.mingle.inbox.model.InboxMediaInfo
import com.mingle.inbox.model.InboxMessage
import com.mingle.inbox.model.InboxPhotoInfo
import com.mingle.inbox.model.InboxUser
import com.mingle.inbox.model.eventbus.net.InboxBaseEvent
import com.mingle.inbox.model.eventbus.net.InboxCanSendMessageEvent
import com.mingle.inbox.model.eventbus.net.InboxConversationMediaChangedEvent
import com.mingle.inbox.model.eventbus.net.InboxDeleteMessageEvent
import com.mingle.inbox.model.eventbus.net.InboxGetLatestMessageEvent
import com.mingle.inbox.model.eventbus.net.InboxGetMoreMessageEvent
import com.mingle.inbox.model.eventbus.net.InboxSendMessageEvent
import com.mingle.inbox.model.eventbus.net.InboxUpdateEncounterLikeEvent
import com.mingle.inbox.model.eventbus.net.InboxUploadAudioEvent
import com.mingle.inbox.model.eventbus.net.InboxUploadPhotosEvent
import com.mingle.inbox.model.eventbus.net.InboxUploadVideoEvent
import com.mingle.inbox.model.eventbus.net.NewConversationEvent
import com.mingle.inbox.model.eventbus.pusher.InboxConversationSeenEvent
import com.mingle.inbox.model.eventbus.pusher.InboxMessageCreatedEvent
import com.mingle.inbox.model.eventbus.pusher.InboxMessageDeletedEvent
import com.mingle.inbox.model.eventbus.pusher.InboxRewindEncounterLikeEvent
import com.mingle.inbox.model.response.CanSendMessageResponse
import com.mingle.inbox.services.InboxService
import com.mingle.inbox.utils.InboxUtils.isUnreadConversation
import com.mingle.sticker.delegate.InputBarActionHandler
import com.mingle.sticker.fragments.sticker.StickerHolderFragment
import com.mingle.sticker.fragments.sticker.StickerHolderFragment.OnInputBarReadyListener
import com.mingle.sticker.models.GiphyData
import com.mingle.sticker.models.InputBarData
import com.mingle.sticker.widget.StickerInputBar
import com.mingle.twine.R
import com.mingle.twine.TwineApplication
import com.mingle.twine.activities.BaseTwineActivity
import com.mingle.twine.activities.MainActivity
import com.mingle.twine.activities.feedusers.FeedProfileActivity.Companion.start
import com.mingle.twine.activities.inbox.InboxConversationActivity
import com.mingle.twine.activities.mymedia.CameraActivity
import com.mingle.twine.activities.poweraccount.PowerAccountActivity
import com.mingle.twine.activities.verifyaccount.VerifyPhotoActivity
import com.mingle.twine.activities.verifyaccount.VerifyPhotoActivity.Companion.newIntent
import com.mingle.twine.data.UserDataManager.canChatToUser
import com.mingle.twine.data.UserDataManager.isUserFlagged
import com.mingle.twine.data.UserSharedPrefs
import com.mingle.twine.databinding.FragmentInboxConversationBinding
import com.mingle.twine.databinding.LayoutInboxRemindedBinding
import com.mingle.twine.databinding.LayoutInboxSourceBinding
import com.mingle.twine.extensions.fadeVisibility
import com.mingle.twine.fragments.BaseFragment
import com.mingle.twine.fragments.ai.IcebreakerGeneratorFragment
import com.mingle.twine.fragments.dialog.InboxFlashMessagePreviewDialogFragment
import com.mingle.twine.fragments.dialog.OutOfCoinDialog
import com.mingle.twine.fragments.dialog.SayHiConfirmDialogFragment.SayHiConfirmDialogFragmentInteraction
import com.mingle.twine.fragments.dialog.report.ReportMessageDialogFragment
import com.mingle.twine.fragments.dialog.report.ReportMessageDialogFragment.Companion.openReportMessageDialog
import com.mingle.twine.fragments.dialog.report.ReportUserDialogFragment.Companion.openReportUserDialog
import com.mingle.twine.gallery.ui.MediaPreviewActivity
import com.mingle.twine.gallery.ui.PhotoFragment
import com.mingle.twine.gallery.ui.PhotoFragment.Companion.newInstance
import com.mingle.twine.gallery.ui.PhotoFragment.OnMediaClickListener
import com.mingle.twine.gallery.ui.adapter.AlbumMediaAdapter.MediaClickEvent
import com.mingle.twine.models.AiGeneratedContent
import com.mingle.twine.models.AudioMessageModel
import com.mingle.twine.models.FeedUser
import com.mingle.twine.models.IceBreakMessage
import com.mingle.twine.models.PlayerStateModel
import com.mingle.twine.models.TwineConstants
import com.mingle.twine.models.User
import com.mingle.twine.models.UserPhoto.Companion.getSmallUrl
import com.mingle.twine.models.UserVideo.Companion.getVideoLargeThumbnail
import com.mingle.twine.models.eventbus.OpenMeetEvent
import com.mingle.twine.models.eventbus.PowerAccountUpdatedEvent
import com.mingle.twine.models.eventbus.UnblockUserEvent
import com.mingle.twine.models.eventbus.VerificationPhotoEvent
import com.mingle.twine.models.response.BaseError
import com.mingle.twine.net.credentials.TwineCredentials
import com.mingle.twine.repository.AppRepository
import com.mingle.twine.utils.MlKitDetector.isNSFW
import com.mingle.twine.utils.MlKitDetector.isVideoNSFW
import com.mingle.twine.utils.SingleDebounceClickListener
import com.mingle.twine.utils.TwineDialogHelper
import com.mingle.twine.utils.TwineSessionManager
import com.mingle.twine.utils.TwineTutorialHelper
import com.mingle.twine.utils.TwineUtils.censorName
import com.mingle.twine.utils.TwineUtils.copyToClipboard
import com.mingle.twine.utils.TwineUtils.formatSpanString
import com.mingle.twine.utils.TwineUtils.isNullOrEmpty
import com.mingle.twine.utils.ads.callbacks.AppodealInterstitialCallbacks
import com.mingle.twine.utils.player.AudioPlayer
import com.mingle.twine.utils.player.SingleAudioPlayer
import com.mingle.twine.utils.scheduler.SchedulerUtils
import com.mingle.twine.utils.tracking.TrackingParams
import com.mingle.twine.utils.tracking.TrackingScreenNames
import com.mingle.twine.utils.tracking.TrackingUtil
import com.mingle.twine.views.adapters.BottomEndlessRecyclerViewScrollListener
import com.mingle.twine.views.adapters.IceBreakerAdapter
import com.mingle.twine.views.adapters.IceBreakerAdapter.OnIceBreakerSelectListener
import com.mingle.twine.views.adapters.InboxMessagesRecycleViewAdapter
import com.mingle.twine.views.adapters.InboxMessagesRecycleViewAdapter.OnItemRetryClickListener
import com.mingle.twine.views.adapters.inbox.ConversationMediaPromptModel_
import com.mingle.twine.views.adapters.viewholder.MessageViewHolderListener
import com.uber.autodispose.AutoDispose
import com.uber.autodispose.SingleSubscribeProxy
import com.uber.autodispose.android.lifecycle.AndroidLifecycleScopeProvider
import io.reactivex.Completable
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import retrofit2.HttpException
import timber.log.Timber
import java.io.File
import java.util.Locale
import com.mingle.twine.data.UserDataManager.user as myUser

class InboxConversationFragment : BaseFragment(), MessageViewHolderListener, OnInputBarReadyListener, OnIceBreakerSelectListener, OnMediaClickListener {
    private lateinit var binding: FragmentInboxConversationBinding
    private var myInboxUser: InboxUser? = null
    private var friendInboxUser: InboxUser? = null
    private var conversationId = 0
    private var targetInboxUserId = 0
    private var isDirectChatByAd = false
    private var isLoadedAllMessages = false
    private var isStopLoadMoreMessage = false
    private var isIceBreakerAnimating = false
    private var isIcebreakerGeneratorEnabled = false

    private var conversation: InboxConversation? = null
    private var messagesAdapter: InboxMessagesRecycleViewAdapter? = null
    private val mainHandler = Handler(Looper.getMainLooper())

    private var audioPlayer: AudioPlayer? = null

    var inputBar: StickerInputBar? = null
        private set
    private var inboxFlashMessagePreviewDialogFragment: InboxFlashMessagePreviewDialogFragment? = null
    private var iceBreakerAdapter: IceBreakerAdapter? = null

    private var photoFragment: PhotoFragment? = null

    private var lastSeenMessageId: Long = 0
    private var targetName: String? = null
    private var canSendMessageWrapper: CanSendMessageWrapper? = null
    private var friendFeedUser: FeedUser? = null

    private val onScrollListener: RecyclerView.OnScrollListener = object : RecyclerView.OnScrollListener() {
        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            super.onScrolled(recyclerView, dx, dy)
            if (!recyclerView.canScrollVertically(1)) { // cannot scrolling down
                binding.tvNewMessage.visibility = View.INVISIBLE
            }
            checkToShowScrollToBottomFab()
        }
    }

    private var endlessListener: BottomEndlessRecyclerViewScrollListener? = null

    private val onRecyclerViewLayoutChanged = OnLayoutChangeListener { v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
        endlessListener?.onScrolled(binding.rvMessages, 0, 0)
    }

    private val onItemClickListener: InboxMessagesRecycleViewAdapter.OnItemClickListener = object : InboxMessagesRecycleViewAdapter.OnItemClickListener {
        override fun onUserAvatarClick(ivUserAvatar: View, position: Int) {
            messagesAdapter?.getItem(position)?.user?.let { openUserProfile(it) }
        }

        override fun onMessageClick(position: Int) {
            getActivityContext { activity: FragmentActivity? ->
                messagesAdapter?.let {
                    it.notifyItemChanged(position)
                    val inboxMessage = it.getItem(position)
                    if (inboxMessage != null && inboxMessage.isNotEligibleToChat) {
                        if (activity is BaseTwineActivity) {
                            activity.increaseNumOfPageToShowInterstitialAds(object : AppodealInterstitialCallbacks() {
                                override fun onNextAction() {
                                    val intent = Intent(activity, MainActivity::class.java)
                                    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
                                    intent.putExtra(MainActivity.EXTRA_SOURCE, InboxConversationFragment::class.java.simpleName)
                                    startActivity(intent)
                                    EventBus.getDefault().post(OpenMeetEvent())
                                }
                            })
                        }
                    }
                }
            }
        }

        override fun onReportMessageClick(position: Int) {
            getActivityContext { activity: FragmentActivity ->
                safeLet2(messagesAdapter, friendFeedUser, { messagesAdapter, friendFeedUser ->
                    val inboxMessage = messagesAdapter.getItem(position)
                    if (inboxMessage != null) {
                        if (!inboxMessage.isReported) {
                            openReportMessageDialog(activity, inboxMessage.id, friendUserName, ReportMessageDialogFragment.Callback { _: Long ->
                                try {
                                    inboxMessage.isReported = true
                                    val index = conversation?.messages?.indexOf(inboxMessage) ?: -1
                                    if (index != -1) {
                                        messagesAdapter.notifyItemChanged(index)
                                    }
                                    if (!isUserFlagged(friendFeedUser)) {
                                        TwineDialogHelper.showConfirmDialog(
                                            context, "", getString(R.string.flag_report_this_user_also),
                                            { _: View? ->
                                                openReportUserDialog(
                                                    requireContext(), friendFeedUser.id, friendFeedUser.inboxUserId,
                                                    friendUserName
                                                )
                                            }, { _: View? ->
                                                if (activity is BaseTwineActivity) {
                                                    activity.checkToShowCsFeedback()
                                                }
                                            })
                                    }
                                } catch (ignore: Exception) {
                                }
                            })
                        } else {
                            showToastSafe(activity, activity.getString(R.string.flag_message_already))
                        }
                    }
                })
            }
        }

        override fun requestMediaEnablePermission(position: Int) {
            TwineDialogHelper.showSimpleDialog(
                activity,
                getString(com.mingle.inbox.R.string.conversation_media_enable),
                getString(com.mingle.inbox.R.string.conversation_media_enable_guide),
                null
            )
        }
    }

    private fun showMessageSelectionAction(context: Context, inboxMessage: InboxMessage, actions: Array<String>) {
        val builder = AlertDialog.Builder(context)
        builder.setTitle(null)
        builder.setItems(actions) { _: DialogInterface?, position: Int ->
            if (context.getString(R.string.tw_copy).equals(actions[position], ignoreCase = true)) {
                copyToClipboard(context, inboxMessage.content)
            } else if (getString(R.string.tw_delete).equals(actions[position], ignoreCase = true)) {
                confirmDeleteMessage(context, inboxMessage)
            }
        }
        builder.show()
    }

    private fun confirmDeleteMessage(context: Context, inboxMessage: InboxMessage) {
        TwineDialogHelper.showConfirmDialog(
            context, "", getString(com.mingle.inbox.R.string.inbox_message_delete),
            { _: View? ->
                safeLet2(TwineApplication.instance.getInboxService(), conversation, { inboxService, conversation ->
                    inboxService.deleteMessage(conversation.id, inboxMessage)
                })
            }, null
        )
    }

    private val onItemLongClickListener: InboxMessagesRecycleViewAdapter.OnItemLongClickListener = object : InboxMessagesRecycleViewAdapter.OnItemLongClickListener {
        override fun onNormalMessageLongClick(view: View, position: Int, isCopyable: Boolean, isDeletable: Boolean) {
            getActivityContext { activity: FragmentActivity ->
                messagesAdapter?.getItem(position)?.let { inboxMessage ->
                    if (isCopyable && isDeletable) {
                        val actions = arrayOf(getString(R.string.tw_copy), getString(R.string.tw_delete))
                        showMessageSelectionAction(activity, inboxMessage, actions)
                    } else if (isCopyable) {
                        val actions = arrayOf(getString(R.string.tw_copy))
                        showMessageSelectionAction(activity, inboxMessage, actions)
                    } else if (isDeletable) {
                        confirmDeleteMessage(activity, inboxMessage)
                    }
                }
            }
        }

        override fun onFlashMessageClick(view: View, position: Int, isLeft: Boolean) {
            safeLet2(TwineApplication.instance.getInboxService(), messagesAdapter?.getItem(position), { _, inboxMessage ->
                if (!inboxMessage.giphyContent?.mobileUrl.isNullOrEmpty()) {
                    inboxFlashMessagePreviewDialogFragment = InboxFlashMessagePreviewDialogFragment.newPhotoMessagePreviewInstance(inboxMessage.giphyContent?.mobileUrl)

                } else if (!inboxMessage.content.isNullOrEmpty() && inboxMessage.content?.contains(InboxMessagesRecycleViewAdapter.STICKER) != true) {
                    inboxFlashMessagePreviewDialogFragment = InboxFlashMessagePreviewDialogFragment.newTextMessagePreviewInstance(inboxMessage.content)

                } else if (inboxMessage.attachedPhoto != null && (InboxMessage.MESSAGE_TYPE_STICKER != inboxMessage.messageType)
                    && (!inboxMessage.photoPath.isNullOrEmpty() || !inboxMessage.attachedPhoto?.photoUrl.isNullOrEmpty())
                ) {
                    var photoUri = inboxMessage.attachedPhoto?.photoUrl
                    if (inboxMessage.isLocal && !inboxMessage.photoPath.isNullOrEmpty()) {
                        inboxMessage.photoPath?.let {
                            val file = File(it)
                            if (file.exists()) {
                                photoUri = "file://$it"
                            }
                        }
                    }
                    inboxFlashMessagePreviewDialogFragment = InboxFlashMessagePreviewDialogFragment.newPhotoMessagePreviewInstance(photoUri)
                } else if (inboxMessage.attachedAudio != null && (!inboxMessage.audioPath.isNullOrEmpty() || !inboxMessage.attachedAudio?.audioUrl.isNullOrEmpty())) {
                    var audioUri = inboxMessage.attachedAudio?.audioUrl
                    if (inboxMessage.isLocal && !inboxMessage.audioPath.isNullOrEmpty()) {
                        inboxMessage.audioPath?.let {
                            val file = File(it)
                            if (file.exists()) {
                                audioUri = it
                            }
                        }
                    }
                    inboxFlashMessagePreviewDialogFragment = InboxFlashMessagePreviewDialogFragment.newAudioMessagePreviewInstance(audioUri, position)

                } else if (inboxMessage.attachedVideo != null && (!inboxMessage.videoPath.isNullOrEmpty() || !inboxMessage.attachedVideo?.videoUrl.isNullOrEmpty())) {
                    var videoThumbnailUri = inboxMessage.attachedVideo?.thumbnailUrl
                    var videoUri = inboxMessage.attachedVideo?.videoUrl
                    if (inboxMessage.isLocal && !inboxMessage.videoPath.isNullOrEmpty()) {
                        inboxMessage.videoPath?.let {
                            val file = File(it)
                            if (file.exists()) {
                                videoThumbnailUri = it
                                videoUri = it
                            }
                        }
                    }
                    inboxFlashMessagePreviewDialogFragment = InboxFlashMessagePreviewDialogFragment
                        .newVideoMessagePreviewInstance(inboxMessage.conversationId, inboxMessage.id, videoThumbnailUri, videoUri)

                } else if (!inboxMessage.stickerUrl.isNullOrEmpty()) {
                    inboxFlashMessagePreviewDialogFragment = InboxFlashMessagePreviewDialogFragment.newPhotoMessagePreviewInstance(inboxMessage.stickerUrl)
                }
                inboxFlashMessagePreviewDialogFragment?.let {
                    it.setOnFlashChatClosedListener {
                        if (isLeft) {
                            sendFlashMessageViewed(inboxMessage)
                            // remove leftMessage after 1s
                            mainHandler.postDelayed({ onFlashChatDone(position) }, TIME_AFTER_VIEW_AUTO_DELETE.toLong())
                        }
                    }
                    it.show(parentFragmentManager, InboxFlashMessagePreviewDialogFragment::class.java.simpleName)
                }
            })
        }
    }

    private fun sendFlashMessageViewed(inboxMessage: InboxMessage) {
        val inboxService = TwineApplication.instance.getInboxService()
        if (inboxService != null && inboxMessage.viewedAt == 0L) {
            inboxMessage.viewedAt = System.currentTimeMillis()
            inboxMessage.isViewingFlashMessage = true
            inboxService.sendReadFlashMessage(inboxMessage)
        }
    }

    private val mOnItemRetryClickListener = OnItemRetryClickListener { inboxMessage: InboxMessage -> this.retryToSendInboxMessage(inboxMessage) }

    private fun initInputBar() {
        val stickerHolderFragment = StickerHolderFragment.newInstance(this, TwineCredentials.GIPHY_KEY)
        val lFragmentManager = childFragmentManager
        lFragmentManager.beginTransaction()
            .replace(R.id.inputbar_holder_frame, stickerHolderFragment)
            .commitAllowingStateLoss()
        lFragmentManager.executePendingTransactions()
        binding.stickerKeyboardLayout.setListener { isShown: Boolean ->
            if (isShown && binding.layoutIceBreakerContent.isVisible) {
                binding.layoutIceBreakerContent.requestLayout()
            }
        }
    }

    private val mInputBarActionHandler: InputBarActionHandler = object : InputBarActionHandler {
        override fun sendMessage(data: InputBarData) {
            getActivityContext { activity: FragmentActivity ->
                if (data.type == InputBarData.Type.PHOTO) {
                    Completable
                        .fromAction {
                            activity.runOnUiThread { (activity as InboxConversationActivity).showLoading(false) }
                            val photoBitmap = Glide.with(activity).asBitmap().load(File(data.photoPath)).submit().get()
                            val confidenceThreshold = 0.85f
                            isNSFW(photoBitmap, confidenceThreshold) { isNSFW: Boolean, confidence: Float?, bitmap: Bitmap? ->
                                if (isNSFW) {
                                    val title = getString(R.string.tw_warning)
                                    val content = getString(R.string.tw_warning_photo_nudity)
                                    TwineDialogHelper.showSimpleDialog(activity, title, content, null)
                                } else {
                                    activity.runOnUiThread {
                                        val inboxMessage = createInboxMessage(data)
                                        sendInboxMessage(inboxMessage)
                                        hideAlbumFragment()
                                    }
                                }
                                clearInputBarContent()
                            }
                        }
                        .subscribeOn(Schedulers.io())
                        .`as`(
                            AutoDispose.autoDisposable<Any>(
                                AndroidLifecycleScopeProvider.from(ProcessLifecycleOwner.get().lifecycle, Lifecycle.Event.ON_DESTROY)
                            )
                        )
                        .subscribe({ (activity as InboxConversationActivity).hideLoading() }, { throwable: Throwable ->
                            activity.runOnUiThread { (activity as InboxConversationActivity).hideLoading() }
                            throwable.printStackTrace()
                        })
                } else if (data.type == InputBarData.Type.VIDEO) {
                    Completable
                        .fromAction {
                            activity.runOnUiThread { (activity as InboxConversationActivity).showLoading(false) }
                            val confidenceThreshold = 0.85f
                            isVideoNSFW(
                                data.videoPath, confidenceThreshold,
                                { isNSFW: Boolean, confidence: Float?, videoPath: String? ->
                                    if (isNSFW) {
                                        val title = getString(R.string.tw_warning)
                                        val content = getString(R.string.tw_warning_video_nudity)
                                        TwineDialogHelper.showSimpleDialog(activity, title, content, null)
                                    } else {
                                        activity.runOnUiThread {
                                            val inboxMessage = createInboxMessage(data)
                                            sendInboxMessage(inboxMessage)
                                            hideAlbumFragment()
                                        }
                                    }
                                    clearInputBarContent()
                                })
                        }
                        .subscribeOn(Schedulers.io())
                        .`as`(
                            AutoDispose.autoDisposable<Any>(
                                AndroidLifecycleScopeProvider.from(
                                    ProcessLifecycleOwner.get().lifecycle, Lifecycle.Event.ON_DESTROY
                                )
                            )
                        )
                        .subscribe({ (activity as InboxConversationActivity).hideLoading() }, { throwable: Throwable ->
                            activity.runOnUiThread { (activity as InboxConversationActivity).hideLoading() }
                            throwable.printStackTrace()
                        })
                } else {
                    val inboxMessage = createInboxMessage(data)
                    sendInboxMessage(inboxMessage)
                    clearInputBarContent()
                }
            }
        }

        override fun sendNotifyMessage(message: String) {
            getActivityContext { activity: FragmentActivity? -> showToastSafe(activity, message, Toast.LENGTH_LONG) }
        }

        override fun openGallery() {
            getActivityContext { activity: FragmentActivity ->
                if (!(activity as BaseTwineActivity).requestMediaPermissions()) {
                    showPhoto(activity, true)
                }
            }
        }

        override fun openCamera() {
            getActivityContext { activity: FragmentActivity ->
                (activity as InboxConversationActivity).checkCameraPermission(
                    this@InboxConversationFragment, CameraActivity.TYPE_PHOTO_ONLY, true, isEnableAutoDelete
                )
            }
        }

        override fun onEnableFlashChatMode() {
            // no need flashchat
        }

        override fun onEnableNormalChatMode() {
            // no need flashchat
        }

        override fun sendGiphy(data: GiphyData) {
            val inboxMessage = createInboxGiphyMessage(data)
            if (inboxMessage != null) {
                sendInboxMessage(inboxMessage)
                clearInputBarContent()
                TrackingUtil.logDirectMsgSentEvent(InboxMessage.MESSAGE_TYPE_GIPHY)
            }
        }

        override fun onGiphyShowUp(isShow: Boolean) {
            if (isShow && (binding.layoutIceBreakerContent.isVisible || binding.btnGenerateIcebreaker.isVisible)) {
                hideIceBreakerMessage()
            } else if (!isShow && iceBreakerAdapter != null) {
                // it mean icebreaker already shown => just show again
                checkToShowIceBreaker()
                checkToShowPowerAccountAd()
                checkToUpdateInputBarStatus()
            }
            binding.imageGiphy.visibility = if (isShow) View.VISIBLE else View.GONE
        }

        override fun hidePhoto(): Boolean {
            activity?.let { return showPhoto(it, false) }
            return false
        }

        override fun onAudioClick(imageViewForTutorial: View) {
            getActivityContext { activity: FragmentActivity ->
                if (UserSharedPrefs.getInstance().needToShowAutoDeleteTutorial(activity) && isEnableAutoDelete) {
                    val tooltip = TwineTutorialHelper.createAutoDeleteToolTips(activity, imageViewForTutorial, null)
                    tooltip.showWithAnimation()
                    UserSharedPrefs.getInstance().setNeedToShowAutoDeleteTutorial(activity, false)
                }
            }
        }
    }

    private val appBarOffsetChangedListener = BaseOnOffsetChangedListener<AppBarLayout> { appBarLayout, verticalOffset ->
        try {
            val messageThreshold = 6 //still update margin if number of message less than threshold to prevent message hidden below input bar
            val layoutParams = binding.rvMessages.layoutParams as MarginLayoutParams
            if (layoutParams.bottomMargin > 0 || (messagesAdapter?.itemCount ?: 0) < messageThreshold) {
                val marginBottom = appBarLayout.measuredHeight + verticalOffset
                if (binding.rvMessages.canScrollVertically(-1) || binding.rvMessages.canScrollVertically(1)) {
                    if (verticalOffset < 0) {
                        layoutParams.setMargins(0, 0, 0, marginBottom)
                        binding.rvMessages.requestLayout()
                    }
                } else {
                    layoutParams.setMargins(0, 0, 0, marginBottom)
                    binding.rvMessages.requestLayout()
                }
            }
            conversation?.let {
                if (it.isSourceEncounter) {
                    if (appBarLayout.measuredHeight + verticalOffset <= binding.viewEncounterCollapsed.content.height) {
                        if (binding.viewEncounterCollapsed.content.visibility != View.VISIBLE) {
                            binding.viewEncounterCollapsed.content.fadeVisibility(View.VISIBLE, 300)
                        }
                    } else {
                        if (binding.viewEncounterCollapsed.content.visibility != View.INVISIBLE) {
                            binding.viewEncounterCollapsed.content.fadeVisibility(View.INVISIBLE, 300)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun showPhoto(activity: FragmentActivity, isShow: Boolean): Boolean {
        val fragmentManager = activity.supportFragmentManager
        photoFragment = fragmentManager.findFragmentByTag(PhotoFragment::class.java.name) as PhotoFragment?
        if (photoFragment == null) {
            photoFragment = newInstance(true, true, true, false)
        }
        photoFragment?.let {
            it.mOnMediaClickListener = this
            if (isShow) {
                if (!it.isAdded) {
                    fragmentManager.beginTransaction()
                        .add(R.id.photo_fragment, it, PhotoFragment::class.java.name)
                        .show(it).commitAllowingStateLoss()
                } else {
                    fragmentManager.beginTransaction().show(it).commitAllowingStateLoss()
                }
                return true
            } else {
                val needHide = it.isVisible
                if (it.isAdded) {
                    fragmentManager.beginTransaction().hide(it).commitAllowingStateLoss()
                }
                return needHide
            }
        }

        return false
    }

    fun hideAlbumFragment(): Boolean {
        photoFragment?.let {
            if (it.isAdded && !it.isMinimize()) {
                it.collapse()
                return true
            }
        }
        return false
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val args = arguments
        if (args != null) {
            conversationId = args.getInt(CONVERSATION_ID, 0)
            targetInboxUserId = args.getInt(TARGET_INBOX_USER_ID, 0)
            targetName = args.getString(TARGET_NAME)
            isDirectChatByAd = args.getBoolean(IS_DIRECT_CHAT_BY_AD)
        }
        isIcebreakerGeneratorEnabled = TwineSessionManager.getInstance().isIcebreakerGeneratorEnabled
    }

    override fun onResume() {
        super.onResume()
        updateConversationStatus()
        setConversationRead()
    }

    fun setFriendFeedUser(friendFeedUser: FeedUser?) {
        this.friendFeedUser = friendFeedUser
    }

    private fun setConversationRead() {
        val inboxService = TwineApplication.instance.getInboxService()
        if (inboxService != null && isUnreadConversation(conversation)) {
            sendReadMessageAndSeen()
            conversation?.shouldMarkConversationAsSeen = true
        }
    }

    override fun onStop() {
        safeLet2(TwineApplication.instance.getInboxService(), conversation, { inboxService, inboxConversation ->
            if (inboxConversation.shouldMarkConversationAsSeen) {
                inboxService.markConversationAsSeen(inboxConversation)
                inboxService.updateConversationViewedCount(inboxConversation)
            }
        })
        super.onStop()
    }

    override fun onDestroyView() {
        audioPlayer?.release()
        mainHandler.removeCallbacks(notifyAdapterRunnable)
        binding.rvMessages.removeOnLayoutChangeListener(onRecyclerViewLayoutChanged)
        binding.ablMessages.removeOnOffsetChangedListener(appBarOffsetChangedListener)
        super.onDestroyView()
    }

    private fun addAlbumFragment() {
        getActivityContext { activity: FragmentActivity ->
            val fragmentManager = activity.supportFragmentManager
            photoFragment = fragmentManager.findFragmentByTag(PhotoFragment::class.java.name) as PhotoFragment?
            if (photoFragment == null) {
                photoFragment = newInstance(true, true, true, false)
            }
            photoFragment?.let {
                if (!it.isAdded) {
                    fragmentManager.beginTransaction()
                        .add(R.id.photo_fragment, it, PhotoFragment::class.java.name).hide(it).commitAllowingStateLoss()
                }
            }
        }
    }

    public override fun onCreateViewImpl(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        if (context != null) {
            audioPlayer = SingleAudioPlayer(context)
        }
        binding = FragmentInboxConversationBinding.inflate(inflater, container, false)
        initInputBar()
        addAlbumFragment()
        initialize()
        setEventListeners()
        return binding.root
    }

    private fun initialize() {
        getActivityContext { activity: FragmentActivity ->
            TwineApplication.instance.getInboxServiceLiveData().observe(this, object : Observer<InboxService?> {
                override fun onChanged(value: InboxService?) {
                    value?.let { inboxService ->
                        TwineApplication.instance.getInboxServiceLiveData().removeObserver(this)
                        Single
                            .fromCallable {
                                var conversation = value.getLocalConversation(conversationId)
                                if (conversation == null) {
                                    conversation = value.getLocalConversationByInboxUserId(targetInboxUserId)
                                }
                                conversation ?: InboxConversation()
                            }
                            .compose(SchedulerUtils.ioToMain())
                            .`as`<SingleSubscribeProxy<InboxConversation>>(
                                AutoDispose.autoDisposable(
                                    AndroidLifecycleScopeProvider.from(this@InboxConversationFragment, Lifecycle.Event.ON_DESTROY)
                                )
                            )
                            .subscribe({ inboxConversation: InboxConversation ->
                                conversation = if (inboxConversation.id != 0) inboxConversation else null
                                myInboxUser = inboxService.getMyInboxUser(conversation)
                                friendInboxUser = inboxService.getFriendInboxUser(conversation)
                                if (myUser == null || (conversation != null && (myInboxUser == null || friendInboxUser == null))) {
                                    activity.finish()
                                    return@subscribe
                                }

                                safeLet2(
                                    myUser?.inboxUserId, if (conversation == null) targetInboxUserId else friendInboxUser?.inboxUserId,
                                    { senderId, receiverId ->
                                        inboxService.canSendMessage(senderId, receiverId)
                                        (activity as InboxConversationActivity).showLoading(false)
                                    })
                                if (inputBar != null) {
                                    updateConversationAfterInitialize()
                                }
                                if (conversation?.isSourceEncounter == true) {
                                    TrackingUtil.logEncounterConversationOpened(encounterTrackingType)
                                }
                            }, { t: Throwable? -> Log.ePrintLn(t) })
                    }
                }
            })
        }
    }

    private fun initUI() {
        safeLet2(TwineApplication.instance.getInboxService(), conversation, { inboxService, conversation ->
            inboxService.updateSeenMessageIds(conversation)

            initAudioCallback()
            messagesAdapter = InboxMessagesRecycleViewAdapter(
                activity, myUser, conversation, audioPlayer, onItemClickListener,
                onItemLongClickListener, this@InboxConversationFragment, mOnItemRetryClickListener
            )
            binding.rvMessages.adapter = messagesAdapter
            binding.rvMessages.addOnScrollListener(onScrollListener)
            updateRecyclerViewMessagesMargin()
            postAndNotifyAdapter()

            updateUserProfileForConversation()
            if (!TextUtils.isEmpty(friendUserName)) {
                val desc = String.format(Locale.US, getString(R.string.tw_plus_power_account_conversation), friendUserName)
                if (isAdded) {
                    val builder = formatSpanString(desc, null, friendUserName)
                    binding.tvPowerAccount.setText(builder, TextView.BufferType.SPANNABLE)
                }
            }
            scrollToBottom()
            checkToShowConversationSourceAndSafetyTip(true)
            checkToShowCanSendMessage()
            checkToShowIceBreaker()
            checkToShowPowerAccountAd()
            checkToUpdateInputBarStatus()
            checkToShowConversationPrompts()
        })
    }

    private fun updateRecyclerViewMessagesMargin() {
        if (binding.ablMessages.measuredHeight > 0) {
            (binding.rvMessages.layoutParams as MarginLayoutParams)
                .setMargins(0, 0, 0, binding.ablMessages.measuredHeight)
            binding.rvMessages.requestLayout()
            binding.ablMessages.addOnOffsetChangedListener(appBarOffsetChangedListener)
        } else {
            binding.ablMessages.viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    if (binding.ablMessages.viewTreeObserver.isAlive) {
                        binding.ablMessages.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    }
                    updateRecyclerViewMessagesMargin()
                }
            })
        }
    }

    private fun checkToUpdateInputBarStatus() {
        inputBar?.enableAutoDelete = isEnableAutoDelete
        inputBar?.setEnablePhotoAndVideo(conversation != null)
    }

    private fun initAudioCallback() {
        audioPlayer?.addPlayerListener(object : AudioPlayer.PlayerCallback {
            override fun onAudioChanged(lastAudioMessage: AudioMessageModel?, newAudioMessage: AudioMessageModel) {
                messagesAdapter?.let { adapter ->
                    if (lastAudioMessage != null) {
                        val lastAudioInboxMsgPosition = adapter.getInboxMessagePosById(lastAudioMessage.id)
                        if (lastAudioInboxMsgPosition != -1) {
                            adapter.getItem(lastAudioInboxMsgPosition)?.let {
                                it.attachedAudio?.apply {
                                    progress = 0f
                                    state = PlayerStateModel.STATE_STOPPED
                                }
                                adapter.notifyItemChanged(lastAudioInboxMsgPosition, it)
                            }
                        }
                    }

                    val newAudioInboxMsgPosition = adapter.getInboxMessagePosById(newAudioMessage.id)
                    if (newAudioInboxMsgPosition != -1) {
                        adapter.getItem(newAudioInboxMsgPosition)?.let {
                            it.attachedAudio?.apply {
                                progress = newAudioMessage.progress
                                state = newAudioMessage.state
                            }
                            adapter.notifyItemChanged(newAudioInboxMsgPosition, it)
                        }
                    }
                }
            }

            override fun onCompletion(model: AudioMessageModel) {
                messagesAdapter?.let { adapter ->
                    val position = adapter.getInboxMessagePosById(model.id)
                    if (position != -1) {
                        adapter.getItem(position)?.let {
                            it.attachedAudio?.apply {
                                progress = 0f
                                state = PlayerStateModel.STATE_STOPPED
                            }
                            adapter.notifyItemChanged(position, it)
                        }
                    }
                }
            }

            override fun onPlaybackStateUpdated(playerStateModel: PlayerStateModel) {
                messagesAdapter?.let { adapter ->
                    val position = adapter.getInboxMessagePosById(playerStateModel.audioMessageModel.id)
                    if (position != -1) {
                        adapter.getItem(position)?.let {
                            it.attachedAudio?.apply {
                                progress = playerStateModel.audioMessageModel.progress
                                state = playerStateModel.state
                            }
                            adapter.notifyItemChanged(position, it)
                        }
                    }
                }
            }

            override fun onError(message: String) {
            }
        })
    }

    private fun checkToShowPowerAccountAd() {
        var shouldShowAd = false
        safeLet2(myUser, conversation, { myUser, conversation ->
            if (myUser.isNormalAccount && binding.layoutDisable.visibility != View.VISIBLE) {
                val messages: ArrayList<InboxMessage> = conversation.messages
                if (!isNullOrEmpty(messages)) {
                    val latestMessage = messages[messages.size - 1]
                    if (latestMessage.user != null && latestMessage.user?.inboxUserId == myUser.inboxUserId) {
                        shouldShowAd = true
                    }
                }
            }
        })
        canSendMessageWrapper?.let {
            if (it.isNeedToReplyReminded || it.isRequireVerifyPhoto || it.isDeactivated) {
                shouldShowAd = false
            }
        }
        binding.cvPowerAccount.visibility = if (shouldShowAd) View.VISIBLE else View.GONE
    }

    private fun setVerifiedEnabled(enable: Boolean) {
        if (enable) {
            binding.cvVerifyNotice.visibility = View.VISIBLE
            binding.tvVerifyNotice.text = getString(R.string.tw_verify_photo_chat_title)
            hideIceBreakerMessage()
        } else {
            binding.cvVerifyNotice.visibility = View.GONE
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setEventListeners() {
        binding.btnUnblock.setOnClickListener(onClickListener)
        binding.tvNewMessage.setOnClickListener(onClickListener)
        binding.cvPowerAccount.setOnClickListener(onClickListener)
        binding.layoutSayHi.setOnClickListener(onClickListener)
        binding.fabScrollBottom.setOnClickListener(onClickListener)
        binding.cvVerifyNotice.setOnClickListener(onClickListener)
        binding.btnGenerateIcebreaker.setOnClickListener(onClickListener)

        endlessListener = object : BottomEndlessRecyclerViewScrollListener(binding.rvMessages.layoutManager as LinearLayoutManager) {
            override fun onLoadMore(page: Int, totalItemsCount: Int) {
                checkToLoadMore()
            }
        }
        endlessListener?.let { binding.rvMessages.addOnScrollListener(it) }
        binding.rvMessages.addOnLayoutChangeListener(onRecyclerViewLayoutChanged)
    }

    private fun updateUserProfileForConversation() {
        conversation?.let {
            for (i in it.users.indices) {
                if (System.currentTimeMillis() - it.users[i].cacheTime > InboxUser.VALID_CACHE_TIME_IN_MILLISECONDS
                    || TextUtils.isEmpty(it.users[i].profilePhotoUrl)
                ) {
                    Single.fromCallable { TwineApplication.instance.getInboxService()?.getInboxUserIds(it.id) ?: ArrayList() }
                        .compose(SchedulerUtils.ioToMain())
                        .`as`<SingleSubscribeProxy<ArrayList<Int>>>(AutoDispose.autoDisposable(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
                        .subscribe({ listInboxUserId: ArrayList<Int> ->
                            if (!isNullOrEmpty(listInboxUserId)) {
                                TwineApplication.instance.requestUpdateUserProfile(it.id, listInboxUserId, false)
                            }
                        }, { t: Throwable? -> Log.ePrintLn(t) })
                    break
                }
            }
        }
    }

    private fun updateConversationStatus() {
        if (conversation != null) {
            getActivityContext { activity: FragmentActivity? ->
                Single.fromCallable {
                    friendInboxUser?.let { friendInboxUser ->
                        if (!canChatToUser(friendInboxUser.id)) {
                            inputBar?.let {
                                it.clearFocus()
                                KeyboardUtil.hideKeyboard(activity, it.windowToken)
                            }
                            return@fromCallable true
                        }
                    }
                    false
                }.compose(SchedulerUtils.ioToMain())
                    .`as`(AutoDispose.autoDisposable(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
                    .subscribe(
                        { isBlock: Boolean -> this.showBlockConversation(isBlock) },
                        { obj: Throwable -> obj.printStackTrace() })
            }
        }
    }

    private fun showBlockConversation(isBlock: Boolean) {
        binding.layoutDisable.visibility = if (isBlock) View.VISIBLE else View.GONE
    }

    private fun createInboxGiphyMessage(giphyData: GiphyData?): InboxMessage? {
        if (giphyData?.images == null) {
            return null
        }
        val inboxMessage = InboxMessage()
        myUser?.let { inboxMessage.inboxUserId = it.inboxUserId }
        conversation?.let { inboxMessage.conversationId = it.id }
        myInboxUser?.let { inboxMessage.user = it }
        inboxMessage.isLocal = true
        inboxMessage.messageType = InboxMessage.MESSAGE_TYPE_GIPHY
        val content = InboxGiphyContent()
        content.id = giphyData.id
        giphyData.images?.original?.let { content.desktopUrl = it.url }
        giphyData.images?.fixedHeight?.let { content.mobileUrl = it.url }
        inboxMessage.giphyContent = content
        inputBar?.let { inboxMessage.flashDuration = it.flashChatDuration }
        return inboxMessage
    }

    private fun createInboxMessage(data: InputBarData): InboxMessage {
        var msgType: String? = null
        val inboxMessage = InboxMessage()
        myUser?.let { inboxMessage.inboxUserId = it.inboxUserId }
        conversation?.let { inboxMessage.conversationId = it.id }
        myInboxUser?.let { inboxMessage.user = it }
        inboxMessage.isLocal = true
        inboxMessage.flashDuration = data.flashDuration
        @Suppress("WHEN_ENUM_CAN_BE_NULL_IN_JAVA")
        when (data.type) {
            InputBarData.Type.TEXT -> {
                inboxMessage.content = data.textContent
                msgType = TrackingParams.MSG_TYPE_TEXT
            }
            InputBarData.Type.AUDIO -> {
                val audioInfo = InboxMediaInfo()
                inboxMessage.attachedAudio = audioInfo
                inboxMessage.audioPath = data.audioPath
                msgType = TrackingParams.MSG_TYPE_AUDIO
            }
            InputBarData.Type.PHOTO -> {
                val photoInfo = InboxPhotoInfo()
                inboxMessage.attachedPhoto = photoInfo
                inboxMessage.photoPath = data.photoPath
                msgType = TrackingParams.MSG_TYPE_IMAGE
            }
            InputBarData.Type.VIDEO -> {
                val videoInfo = InboxMediaInfo()
                inboxMessage.videoPath = data.videoPath
                inboxMessage.attachedVideo = videoInfo
                msgType = TrackingParams.MSG_TYPE_VIDEO
            }
        }
        TrackingUtil.logDirectMsgSentEvent(msgType)
        return inboxMessage
    }

    private fun createInboxRemindMessage(): InboxMessage {
        val inboxMessage = InboxMessage()
        myUser?.let { inboxMessage.inboxUserId = it.inboxUserId }
        conversation?.let { inboxMessage.conversationId = it.id }
        myInboxUser?.let { inboxMessage.user = it }
        inboxMessage.isLocal = true
        inboxMessage.messageType = InboxMessage.MESSAGE_TYPE_TEXT
        inboxMessage.content = "\uD83D\uDC4B"
        inputBar?.let { inboxMessage.flashDuration = it.flashChatDuration }
        return inboxMessage
    }

    private fun retryToSendInboxMessage(inboxMessage: InboxMessage) {
        val inboxService = TwineApplication.instance.getInboxService()
        inboxService?.sendMessage(conversation, inboxMessage)
    }

    private fun checkValidFileSize(inboxMessage: InboxMessage): Boolean {
        // Photo
        val photoPath = inboxMessage.photoPath
        if (!photoPath.isNullOrEmpty()) {
            return true
        }
        // Video
        val videoPath = inboxMessage.videoPath
        if (!videoPath.isNullOrEmpty()) {
            val videoFile = File(videoPath)
            if (videoFile.length() > S3Util.MAX_FILE_SIZE_VIDEO) {
                getActivityContext { activity: FragmentActivity ->
                    showToastSafe(
                        activity, activity.getString(
                            com.mingle.global.R.string.global_maximum_file_size_is_mb, S3Util.MAX_FILE_SIZE_VIDEO_IN_MB
                        ), Toast.LENGTH_LONG
                    )
                }
                return false
            }
            return true
        }
        // Audio
        val audioPath = inboxMessage.audioPath
        if (!audioPath.isNullOrEmpty()) {
            val audioFile = File(audioPath)
            if (audioFile.length() > S3Util.MAX_FILE_SIZE_AUDIO) {
                getActivityContext { activity: FragmentActivity ->
                    showToastSafe(
                        activity, activity.getString(
                            com.mingle.global.R.string.global_maximum_file_size_is_mb, S3Util.MAX_FILE_SIZE_AUDIO_IN_MB
                        ), Toast.LENGTH_LONG
                    )
                }
                return false
            }
            return true
        }
        return true
    }

    private fun sendInboxMessage(inboxMessage: InboxMessage) {
        if (checkValidFileSize(inboxMessage)) {
            safeLet2(TwineApplication.instance.getInboxService(), myUser, { inboxService, user ->
                if (conversation != null) {
                    addVirtualMessage(inboxMessage)
                    inboxService.sendMessage(conversation, inboxMessage)
                    checkToShowPowerAccountAd()
                    hideIceBreakerMessage()
                } else {
                    getActivityContext { activity: FragmentActivity -> (activity as BaseTwineActivity).showLoading(false) }
                    inboxService.sendDirectMessage(inboxMessage, targetInboxUserId, user.name ?: "", isDirectChatByAd)
                    hideIceBreakerMessage()
                }
            })
        }
    }

    private fun hideIceBreakerMessage() {
        if (isIcebreakerGeneratorEnabled) {
            binding.btnGenerateIcebreaker.visibility = View.GONE
        } else {
            binding.touchView.visibility = View.GONE
            binding.layoutIceBreakerContent.visibility = View.GONE
            binding.rvMessages.translationY = 0f
        }
    }

    private fun addVirtualMessage(inboxMessage: InboxMessage) {
        conversation?.messages?.add(inboxMessage)
        conversation?.lastMessageCreatedAt = inboxMessage.createdAt
        if (!binding.rvMessages.isComputingLayout) {
            postAndNotifyAdapter()
        }
        scrollToBottom()
    }

    private fun scrollToBottom() {
        binding.tvNewMessage.visibility = View.INVISIBLE
        binding.fabScrollBottom.visibility = View.GONE
        messagesAdapter?.let {
            if (binding.rvMessages.layoutManager != null && it.itemCount > 0) {
                binding.rvMessages.scrollToPosition(it.itemCount - 1)
            }
        }
    }

    private fun postAndNotifyAdapter() {
        mainHandler.removeCallbacks(notifyAdapterRunnable)
        mainHandler.post(notifyAdapterRunnable)
        lastSeenMessageId = currentSeenMessageId
    }

    private val notifyAdapterRunnable = Runnable {
        if (activity?.isFinishing == true || !isAdded || isDetached || isRemoving) {
            return@Runnable
        }
        messagesAdapter?.let {
            if (!binding.rvMessages.isComputingLayout) {
                it.submitChange()
                checkToShowConversationSourceAndSafetyTip(false)
            } else {
                postAndNotifyAdapter()
            }
        }
    }

    private fun checkToShowConversationSourceAndSafetyTip(forceUpdate: Boolean) {
        val conv = conversation
        if (conv != null && !conv.haveInteracted() && !conv.isSourceEncounter) {
            binding.viewEncounter.content.visibility = View.GONE
            binding.viewEncounterCollapsed.content.visibility = View.GONE
            binding.viewSafetyTip.clSafetyTip.visibility = View.GONE
            if (!binding.vsLayoutSource.isInflated) {
                binding.vsLayoutSource.viewStub?.inflate()
            }
            binding.vsLayoutSource.root.visibility = View.VISIBLE
            if (forceUpdate) {
                val inboxSourceBinding = binding.vsLayoutSource.binding as LayoutInboxSourceBinding?
                if (inboxSourceBinding != null) {
                    inboxSourceBinding.viewSafetyTip.clSafetyTip.visibility = View.VISIBLE
                    inboxSourceBinding.clSourceHi.visibility = View.GONE
                    inboxSourceBinding.clSourceLike.visibility = View.GONE
                    inboxSourceBinding.clSourceHotPick.visibility = View.GONE
                    inboxSourceBinding.clSourceMatched.visibility = View.GONE
                    if (InboxConversation.SOURCE_HI.equals(conv.conversationSource, ignoreCase = true)) {
                        inboxSourceBinding.clSourceHi.visibility = View.VISIBLE
                    } else if (InboxConversation.SOURCE_LIKE.equals(conv.conversationSource, ignoreCase = true)) {
                        inboxSourceBinding.clSourceLike.visibility = View.VISIBLE
                    } else if (InboxConversation.SOURCE_HOT_PICK.equals(conv.conversationSource, ignoreCase = true)) {
                        inboxSourceBinding.clSourceHotPick.visibility = View.VISIBLE
                    } else {
                        safeLet2(myUser, friendFeedUser, { myUser, friendFeedUser ->
                            if (friendFeedUser.isMatched || myUser.isMatchedWithId(friendFeedUser.id)) {
                                inboxSourceBinding.clSourceMatched.visibility = View.VISIBLE
                                GlideUtil.execute(requireContext()) { action: RequestManager ->
                                    action.load(myUser.smallPrimaryMedia).placeholder(R.drawable.tw_image_holder)
                                        .error(R.drawable.tw_image_holder).dontAnimate().into(inboxSourceBinding.ivUserMe)
                                }
                                if (friendFeedUser.primaryPhoto != null) {
                                    GlideUtil.execute(requireContext()) { action: RequestManager ->
                                        action.load(getSmallUrl(friendFeedUser.primaryPhoto))
                                            .placeholder(R.drawable.tw_image_holder)
                                            .error(R.drawable.tw_image_holder)
                                            .dontAnimate().into(inboxSourceBinding.ivUserFriend)
                                    }
                                } else if (friendFeedUser.primaryVideo != null) {
                                    GlideUtil.execute(requireContext()) { action: RequestManager ->
                                        action.load(getVideoLargeThumbnail(friendFeedUser.primaryVideo))
                                            .placeholder(R.drawable.tw_image_holder)
                                            .error(R.drawable.tw_image_holder)
                                            .dontAnimate().into(inboxSourceBinding.ivUserFriend)
                                    }
                                }
                            }
                        })
                    }
                }
            }
        } else {
            if (binding.vsLayoutSource.isInflated && binding.vsLayoutSource.root.visibility != View.GONE) {
                binding.vsLayoutSource.root.visibility = View.GONE
            }
            if (binding.viewSafetyTip.clSafetyTip.visibility != View.VISIBLE) {
                binding.viewSafetyTip.clSafetyTip.visibility = View.VISIBLE
            }
            if (forceUpdate) {
                if (conv != null && conv.isSourceEncounter) {
                    if (conv.isSourceEncounterZodiac) {
                        val encounterType = String.format("%s - %s", getString(R.string.encounter), getString(R.string.encounter_zodiac))
                        binding.viewEncounter.tvEncounterType.text = encounterType
                        binding.viewEncounterCollapsed.tvEncounterTypeCollapsed.text = encounterType
                    } else if (conv.isSourceEncounterPersonality) {
                        val encounterType = String.format("%s - %s", getString(R.string.encounter), getString(R.string.encounter_personality))
                        binding.viewEncounter.tvEncounterType.text = encounterType
                        binding.viewEncounterCollapsed.tvEncounterTypeCollapsed.text = encounterType
                    }
                    val gradientColors = intArrayOf(
                        ContextCompat.getColor(requireContext(), R.color.encounter_pink_center),
                        ContextCompat.getColor(requireContext(), R.color.encounter_pink_end)
                    )
                    val textShader: Shader = LinearGradient(
                        0f, 0f, 0f,
                        binding.viewEncounter.tvEncounterType.textSize, gradientColors, null, Shader.TileMode.CLAMP
                    )
                    binding.viewEncounter.tvEncounterType.paint.setShader(textShader)
                    updateEncounterStatus()
                    binding.viewEncounter.tvEncounterType.setOnClickListener(onClickListener)
                    binding.viewEncounter.ivEncounterHeart.setOnClickListener(onClickListener)
                    binding.viewEncounterCollapsed.content.setOnClickListener(onClickListener)
                    binding.viewEncounterCollapsed.ivEncounterHeartCollapsed.setOnClickListener(onClickListener)
                    binding.viewEncounter.content.visibility = View.VISIBLE
                    binding.viewEncounterCollapsed.content.visibility = View.INVISIBLE
                } else {
                    binding.viewEncounter.content.visibility = View.GONE
                    binding.viewEncounterCollapsed.content.visibility = View.GONE
                }
            }
        }
    }

    private fun updateEncounterStatus() {
        safeLet2(myInboxUser, friendFeedUser, { myInboxUser, friendFeedUser ->
            binding.viewEncounter.ivEncounterHeart.visibility = View.VISIBLE
            conversation?.let {
                if (it.isEncounterLiked(myInboxUser.inboxUserId) && it.isEncounterLiked(friendFeedUser.inboxUserId)) {
                    binding.viewEncounter.tvEncounterGuide.visibility = View.VISIBLE
                    binding.viewEncounter.tvEncounterGuide2.visibility = View.GONE
                    binding.viewEncounter.tvEncounterGuide3.visibility = View.GONE
                    binding.viewEncounter.tvEncounterGuide.setText(R.string.encounter_found_soul_match)
                    binding.viewEncounter.ivEncounterHeart.setImageResource(R.drawable.ic_encounter_heart_full)
                    binding.viewEncounterCollapsed.ivEncounterHeartCollapsed.setImageResource(R.drawable.ic_encounter_heart_full)
                } else if (it.isEncounterLiked(myInboxUser.inboxUserId) || it.isEncounterLiked(friendFeedUser.inboxUserId)) {
                    if (it.isEncounterLiked(myInboxUser.inboxUserId)) {
                        binding.viewEncounter.tvEncounterGuide.visibility = View.GONE
                        binding.viewEncounter.tvEncounterGuide2.visibility = View.GONE
                        binding.viewEncounter.tvEncounterGuide3.visibility = View.VISIBLE
                        binding.viewEncounter.tvEncounterGuide3.text = getString(R.string.encounter_wait_friend_complete_other_half, friendUserName)
                    } else if (it.isEncounterLiked(friendFeedUser.inboxUserId)) {
                        binding.viewEncounter.tvEncounterGuide.visibility = View.VISIBLE
                        binding.viewEncounter.tvEncounterGuide2.visibility = View.VISIBLE
                        binding.viewEncounter.tvEncounterGuide3.visibility = View.VISIBLE
                        val friendGender = if (User.MALE.equals(friendFeedUser.gender, ignoreCase = true))
                            getString(R.string.tw_say_hi_confirm_dialog_he)
                        else
                            getString(R.string.tw_say_hi_confirm_dialog_she)
                        binding.viewEncounter.tvEncounterGuide.text = getString(R.string.encounter_want_to_know_more_about, friendGender)
                        binding.viewEncounter.tvEncounterGuide2.setText(R.string.encounter_complete_the_heart_below)
                        binding.viewEncounter.tvEncounterGuide3.text =
                            getString(R.string.encounter_friend_completed_their_half, friendUserName, friendGender, friendGender)
                    }
                    binding.viewEncounter.ivEncounterHeart.setImageResource(R.drawable.ic_encounter_heart_half)
                    binding.viewEncounterCollapsed.ivEncounterHeartCollapsed.setImageResource(R.drawable.ic_encounter_heart_half)
                } else {
                    binding.viewEncounter.tvEncounterGuide.visibility = View.VISIBLE
                    binding.viewEncounter.tvEncounterGuide2.visibility = View.VISIBLE
                    binding.viewEncounter.tvEncounterGuide3.visibility = View.GONE
                    val friendGender = if (User.MALE.equals(friendFeedUser.gender, ignoreCase = true))
                        getString(R.string.tw_say_hi_confirm_dialog_he)
                    else
                        getString(R.string.tw_say_hi_confirm_dialog_she)
                    binding.viewEncounter.tvEncounterGuide.text = getString(R.string.encounter_want_to_know_more_about, friendGender)
                    binding.viewEncounter.tvEncounterGuide2.setText(R.string.encounter_complete_the_heart_below)
                    binding.viewEncounter.ivEncounterHeart.setImageResource(R.drawable.ic_encounter_heart_empty)
                    binding.viewEncounterCollapsed.ivEncounterHeartCollapsed.setImageResource(R.drawable.ic_encounter_heart_empty)
                }
            }

        }, {
            binding.viewEncounter.tvEncounterGuide.visibility = View.GONE
            binding.viewEncounter.tvEncounterGuide2.visibility = View.GONE
            binding.viewEncounter.tvEncounterGuide3.visibility = View.GONE
            binding.viewEncounter.ivEncounterHeart.visibility = View.GONE
        })
    }

    private fun openUserProfile(inboxUser: InboxUser) {
        conversation?.let {
            if (!it.shouldHideFriendInfo()) {
                getActivityContext { activity: FragmentActivity? ->
                    if (activity is BaseTwineActivity) {
                        activity.increaseNumOfPageToShowInterstitialAds(object : AppodealInterstitialCallbacks() {
                            override fun onNextAction() {
                                start(activity, inboxUser.id, TrackingScreenNames.SCREEN_CONVERSATION_PROFILE)
                            }
                        })
                    }
                }
            }
        }
    }

    private val onClickListener: View.OnClickListener = object : SingleDebounceClickListener(300) {
        override fun onClickImpl(v: View?) {
            if (v === binding.btnUnblock) {
                getActivityContext { activity: FragmentActivity ->
                    friendInboxUser?.let {
                        (activity as BaseTwineActivity).unblockUser(it.id, it.inboxUserId)
                    }
                }
            } else if (v === binding.tvNewMessage || v === binding.fabScrollBottom) {
                scrollToBottom()
            } else if (v === binding.cvPowerAccount) {
                getActivityContext { activity: FragmentActivity? ->
                    val intent = PowerAccountActivity.getIntent(activity, TrackingScreenNames.SCREEN_CONVERSATION_READ_RECEIPT)
                    startActivity(intent)
                }
            } else if (v === binding.cvVerifyNotice) {
                getActivityContext { activity: FragmentActivity? ->
                    if (activity is BaseTwineActivity) {
                        activity.increaseNumOfPageToShowInterstitialAds(object : AppodealInterstitialCallbacks() {
                            override fun onNextAction() {
                                startActivity(newIntent(activity, VerifyPhotoActivity.Source.CONVERSATION))
                            }
                        })
                    }
                }
            } else if (v === binding.layoutSayHi) {
                sendHiToUser(friendFeedUser)
            } else if (v === binding.btnGenerateIcebreaker) {
                openIcebreakerGenerator()
            } else if (v === binding.viewEncounter.tvEncounterType || v === binding.viewEncounterCollapsed.content) {
                getActivityContext { activity: FragmentActivity? ->
                    conversation?.let {
                        TrackingUtil.logEncounterPopupAction(encounterTrackingType, TrackingParams.ACTION_ENCOUNTER_FIND_OUT)
                        TwineDialogHelper.showSimpleDialog(
                            activity, getString(R.string.encounter_what_make_compatible),
                            it.compatibilityDescription, null, Gravity.START
                        )
                    }
                }
            } else if (v === binding.viewEncounter.ivEncounterHeart || v === binding.viewEncounterCollapsed.ivEncounterHeartCollapsed) {
                safeLet5(
                    myUser, myUser?.channelSetting, myInboxUser, conversation, friendFeedUser,
                    { myUser, channelSetting, myInboxUser, conversation, friendFeedUser ->
                        if (!conversation.isEncounterLiked(myInboxUser.inboxUserId)) {
                            getActivityContext { activity: FragmentActivity? ->
                                if (activity is BaseTwineActivity) {
                                    if (myUser.hasMedia() || !channelSetting.isForcePrimaryMediaToLike) {
                                        if (ArrayUtil.arrayContains(myUser.blockedUserIds, friendFeedUser.id)) {
                                            TwineDialogHelper.showUnblockRequiredConfirmDialog(
                                                activity, "",
                                                getString(R.string.tw_unblock_required),
                                                { _: View? ->
                                                    activity.unblockUser(friendFeedUser.id, friendFeedUser.inboxUserId)
                                                }, null
                                            )
                                        } else {
                                            val params: MutableMap<String, Any> = HashMap()
                                            params[TwineConstants.PARAM_LIKE_SOURCE] = TwineConstants.LIKE_SOURCE_ENCOUNTER
                                            activity.sendLikeToUser(friendFeedUser, params, TrackingScreenNames.SCREEN_CONVERSATION)
                                            val inboxService = TwineApplication.instance.getInboxService()
                                            inboxService?.addConversationEncounterLikeUserId(conversation, myInboxUser.inboxUserId)
                                        }
                                    } else {
                                        activity.showUploadDialog()
                                    }
                                }
                            }
                        }
                    }
                )
            }
        }
    }

    private fun sendHiToUser(friendFeedUser: FeedUser?) {
        safeLet3(myUser, myUser?.channelSetting, friendFeedUser, { myUser, channelSetting, _ ->
            getActivityContext { activity: FragmentActivity ->
                if (myUser.getCredits() >= (channelSetting.spendCreditRules?.sendCharm ?: 0)) {
                    if (UserSharedPrefs.getInstance()
                            .getDisplaySayHiConfirmDialogTime(activity) < TwineConstants.MAX_TIME_DISPLAY_SAY_HI_CONFIRM_DIALOG
                    ) {
                        UserSharedPrefs.getInstance().setDisplaySayHiConfirmDialogTime(
                            activity,
                            UserSharedPrefs.getInstance().getDisplaySayHiConfirmDialogTime(activity) + 1
                        )
                        TwineDialogHelper.showSayHiConfirmDialog(
                            activity, object : SayHiConfirmDialogFragmentInteraction {
                                override fun onYayClick() {
                                    if (activity is BaseTwineActivity) {
                                        TrackingScreenNames.sendHiLikeFromScreen = TrackingScreenNames.SCREEN_CONVERSATION
                                        activity.sendHiToUser(friendFeedUser, BaseTwineActivity.SEND_HI_BY_COIN, javaClass.name)
                                    }
                                }

                                override fun onCancelClick() {
                                }
                            })
                    } else {
                        if (activity is BaseTwineActivity) {
                            TrackingScreenNames.sendHiLikeFromScreen = TrackingScreenNames.SCREEN_CONVERSATION
                            activity.sendHiToUser(friendFeedUser, BaseTwineActivity.SEND_HI_BY_COIN, javaClass.name)
                        }
                    }
                } else {
                    TwineDialogHelper.showOutOfCoinDialog(activity, TrackingParams.CAUSE_SAY_HI, object : OutOfCoinDialog.Callback {
                        override fun onSayHiAdViewed() {
                            TrackingScreenNames.sendHiLikeFromScreen = TrackingScreenNames.SCREEN_CONVERSATION
                            (activity as BaseTwineActivity).sendHiToUser(friendFeedUser, BaseTwineActivity.SEND_HI_BY_ADS, javaClass.name)
                        }
                    })
                }
            }
        })
    }

    private fun openIcebreakerGenerator() {
        inputBar?.let { KeyboardUtil.hideKeyboard(requireActivity(), it.editTextMessage.windowToken) }
        val fragment = childFragmentManager.findFragmentByTag(IcebreakerGeneratorFragment::class.java.name)
        if (fragment == null || (!fragment.isAdded && fragment.isDetached)) {
            val icebreakerGeneratorFragment = IcebreakerGeneratorFragment()
            icebreakerGeneratorFragment.listener = IcebreakerGeneratorFragment.Listener { suggestion: AiGeneratedContent.Suggestion ->
                val inboxMessage = InboxMessage()
                myUser?.let { inboxMessage.inboxUserId = it.inboxUserId }
                conversation?.let { inboxMessage.conversationId = it.id }
                myInboxUser?.let { inboxMessage.user = it }
                inboxMessage.isLocal = true
                inboxMessage.content = suggestion.content
                sendInboxMessage(inboxMessage)
                TrackingUtil.logIceBreakerSent(inboxMessage.id)
                hideIceBreakerMessage()
            }
            val transactionFragment = childFragmentManager.beginTransaction()
            transactionFragment.add(icebreakerGeneratorFragment, IcebreakerGeneratorFragment::class.java.name)
            transactionFragment.commitAllowingStateLoss()
        }
    }

    private val encounterTrackingType: String
        get() {
            conversation?.let {
                if (it.isSourceEncounterZodiac) {
                    return TrackingParams.TYPE_ENCOUNTER_ZODIAC
                } else if (it.isSourceEncounterPersonality) {
                    return TrackingParams.TYPE_ENCOUNTER_PERSONALITY
                }
            }
            return TrackingParams.TYPE_ENCOUNTER_UNKNOWN
        }

    @Subscribe(threadMode = ThreadMode.MAIN)
    override fun onEvent(event: PowerAccountUpdatedEvent) {
        canSendMessageWrapper?.onPowerAccountUpdated(myUser)
        checkToShowCanSendMessage()
        checkToShowIceBreaker()
        checkToUpdateInputBarStatus()
        checkToShowPowerAccountAd()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: VerificationPhotoEvent) {
        if (!canSendMessage() && event.verificationResult != null && event.verificationResult.isApproved) {
            canSendMessageWrapper?.onPhotoVerified()
            checkToShowCanSendMessage()
            checkToShowIceBreaker()
            checkToUpdateInputBarStatus()
            checkToShowPowerAccountAd()
        }
    }

    /*
     *  Inbox Event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: InboxUpdateEncounterLikeEvent) {
        conversation?.let {
            if (it.id == event.conversationId) {
                updateEncounterStatus()
                if (!it.shouldHideFriendInfo()) {
                    messagesAdapter?.notifyAvatarChanged()
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: InboxCanSendMessageEvent) {
        handleCanSendMessageResponse(event.receiverInboxUserId, event.response, event.throwable)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: InboxSendMessageEvent) {
        getActivityContext { activity: FragmentActivity ->
            if (!event.isDirectMessage) {
                conversation?.let { it ->
                    if (event.conversationId == it.id) {
                        postAndNotifyAdapter()
                        scrollToBottom()
                        if (InboxBaseEvent.FAILED.equals(event.status, ignoreCase = true)) {
                            (activity as BaseTwineActivity).hideLoading()
                            event.throwable?.let {
                                val errorMessage = TwineApplication.instance.retrofitHelper?.getErrorMessage(it, getString(R.string.tw_error_unknown))
                                errorMessage?.isNotEmpty()?.let {
                                    TwineDialogHelper.showCommonErrorDialog(context, errorMessage, null)
                                }
                            }
                            checkToShowCanSendMessage()
                            checkToShowPowerAccountAd()
                        }
                    }
                }
            } else {
                if (InboxBaseEvent.SUCCESS.equals(event.status, ignoreCase = true)) {
                    conversationId = event.conversationId
                } else {
                    (activity as BaseTwineActivity).hideLoading()
                    event.throwable?.let {
                        val errorMessage = TwineApplication.instance.retrofitHelper?.getErrorMessage(it, getString(R.string.tw_error_unknown))
                        errorMessage?.isNotEmpty()?.let {
                            TwineDialogHelper.showCommonErrorDialog(context, errorMessage, null)
                        }
                    }
                    checkToShowCanSendMessage()
                    checkToShowPowerAccountAd()
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: InboxDeleteMessageEvent) {
        conversation?.let {
            if (event.conversationId == it.id && event.status.equals(InboxDeleteMessageEvent.SUCCESS, ignoreCase = true)) {
                if (!binding.rvMessages.isComputingLayout) {
                    postAndNotifyAdapter()
                }
                checkToShowPowerAccountAd()
                checkToShowIceBreaker()
                checkToUpdateInputBarStatus()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: InboxMessageCreatedEvent) {
        conversation?.let { it ->
            if (event.conversationId == it.id) {
                if (isUnreadConversation(it) && TwineApplication.instance.isAppInForeground) {
                    sendReadMessageAndSeen()
                    it.shouldMarkConversationAsSeen = true
                }
                if (!binding.rvMessages.isComputingLayout) {
                    postAndNotifyAdapter()
                }
                safeLet2(messagesAdapter, binding.rvMessages.layoutManager as LinearLayoutManager, { adapter, linearLayoutManager ->
                    val bottomOffsetItemPos = linearLayoutManager.itemCount - linearLayoutManager.findLastVisibleItemPosition()
                    val oldSize = adapter.itemCount
                    binding.rvMessages.viewTreeObserver.addOnPreDrawListener(object : ViewTreeObserver.OnPreDrawListener {
                        override fun onPreDraw(): Boolean {
                            if (adapter.itemCount > oldSize) {
                                binding.rvMessages.viewTreeObserver.removeOnPreDrawListener(this)
                                if (inboxFlashMessagePreviewDialogFragment?.isVisible == true) {
                                    binding.tvNewMessage.visibility = View.VISIBLE
                                    binding.fabScrollBottom.visibility = View.GONE
                                } else if (bottomOffsetItemPos <= 3) {
                                    if (binding.rvMessages.canScrollVertically(1)) {
                                        binding.rvMessages.smoothScrollToPosition(adapter.itemCount - 1)
                                    }
                                } else {
                                    binding.fabScrollBottom.visibility = View.GONE
                                    binding.tvNewMessage.visibility = View.VISIBLE
                                }
                            }
                            return true
                        }
                    })
                })
                checkToShowPowerAccountAd()
                checkToShowIceBreaker()
                checkToUpdateInputBarStatus()
                canSendMessageWrapper?.let {
                    if ((it.isNeedToReplyReminded || it.isSentReminded) && containsMatchedMessage()) {
                        val needToReplyReminded = it.isNeedToReplyReminded
                        it.onMatched()
                        if (needToReplyReminded && it.canSendMessage()) {
                            sendInboxMessage(createInboxRemindMessage())
                        }
                    }
                }
                checkToShowCanSendMessage()
            }
        }
    }

    private fun sendReadMessageAndSeen() {
        safeLet2(TwineApplication.instance.getInboxService(), conversation, { inboxService, inboxConversation ->
            inboxService.sendReadMessage(inboxConversation)
            inboxService.markConversationAsSeen(inboxConversation)
        })
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: InboxMessageDeletedEvent) {
        conversation?.let {
            if (event.conversationId == it.id) {
                if (!binding.rvMessages.isComputingLayout) {
                    postAndNotifyAdapter()
                }
                checkToShowPowerAccountAd()
                checkToShowIceBreaker()
                checkToUpdateInputBarStatus()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: InboxUploadVideoEvent) {
        conversation?.let {
            if (it.id == event.conversationId && event.inboxMessage != null) {
                if (event.status.equals(InboxUploadVideoEvent.FAILED, ignoreCase = true)) {
                    getActivityContext { activity: FragmentActivity ->
                        showToastSafe(activity, activity.getString(com.mingle.inbox.R.string.inbox_message_send_failed))
                        it.messages.remove(event.inboxMessage)
                        postAndNotifyAdapter()
                    }
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: InboxUploadPhotosEvent) {
        conversation?.let {
            if (it.id == event.conversationId && event.inboxMessage != null) {
                if (event.status.equals(InboxUploadPhotosEvent.FAILED, ignoreCase = true)) {
                    getActivityContext { activity: FragmentActivity ->
                        showToastSafe(activity, activity.getString(com.mingle.inbox.R.string.inbox_message_send_failed))
                        it.messages.remove(event.inboxMessage)
                        postAndNotifyAdapter()
                    }
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: InboxUploadAudioEvent) {
        conversation?.let {
            if (it.id == event.conversationId && event.inboxMessage != null) {
                if (event.status.equals(InboxUploadAudioEvent.FAILED, ignoreCase = true)) {
                    getActivityContext { activity: FragmentActivity ->
                        showToastSafe(activity, activity.getString(com.mingle.inbox.R.string.inbox_message_send_failed))
                        it.messages.remove(event.inboxMessage)
                        postAndNotifyAdapter()
                    }
                }
            }
        }
    }

    private fun checkToLoadMore() {
        if (isStopLoadMoreMessage || isLoadedAllMessages) return
        safeLet3(
            TwineApplication.instance.getInboxService(), conversation, messagesAdapter,
            { inboxService, conversation, messagesAdapter ->
                if (messagesAdapter.itemCount > 0 && messagesAdapter.getItemViewType(0) != InboxMessage.MINGLE_MESSAGE_TYPE_SYSTEM) {
                    if (conversation.messages.isNotEmpty()) {
                        inboxService.getMoreMessages(conversation.id)
                        binding.progressBar.animate().alpha(1f).setDuration(200).start()
                    }
                }
            }
        )
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: InboxGetLatestMessageEvent) {
        conversation?.let {
            if (it.id == event.conversationId) {
                if (event.status.equals(InboxGetLatestMessageEvent.SUCCESS, ignoreCase = true)) {
                    postAndNotifyAdapter()
                }
                isStopLoadMoreMessage = event.isStopLoadMore
                checkToShowIceBreaker()
                checkToShowPowerAccountAd()
                checkToUpdateInputBarStatus()
                binding.rvMessages.visibility = View.VISIBLE
            }
        }
    }

    private fun checkToShowScrollToBottomFab() {
        val linearLayoutManager = binding.rvMessages.layoutManager as LinearLayoutManager?
        if (linearLayoutManager != null && binding.tvNewMessage.visibility != View.VISIBLE) {
            val lastVisibleItem = linearLayoutManager.findLastVisibleItemPosition()
            val totalItems = linearLayoutManager.itemCount
            if (lastVisibleItem < totalItems - 10) {
                binding.fabScrollBottom.visibility = View.VISIBLE
            } else {
                binding.fabScrollBottom.visibility = View.GONE
            }
        } else {
            binding.fabScrollBottom.visibility = View.GONE
        }
    }

    private fun checkToShowConversationPrompts() {
        safeLet2(TwineApplication.instance.getInboxService(), conversation, { inboxService, conversation ->
            if (conversation.conversationPrompts.any { prompt -> !prompt.closed }) {
                binding.ervMediaPrompts.visibility = View.VISIBLE
                conversation.conversationPrompts.let {
                    binding.ervMediaPrompts.withModels {
                        // The non‐isAskingMediaPermission prompt without buttons
                        it.firstOrNull { prompt -> !prompt.closed && !prompt.isAskingMediaPermission }?.let { prompt ->
                            ConversationMediaPromptModel_()
                                .id(prompt.id)
                                .buttonVisible(false)
                                .promptText(prompt.getPromptText(requireContext()))
                                .onPromptOnClickListener { _ -> inboxService.setConversationMediaPromptClosed(conversationId, prompt.id) }
                                .addTo(this)
                            // Delay marking as read by 1 second
                            mainHandler.postDelayed({ inboxService.markConversationMediaPromptAsRead(conversationId, prompt.id) }, 1000)
                        }
                        // The isAskingMediaPermission prompt with buttons
                        it.firstOrNull { prompt -> !prompt.closed && prompt.isAskingMediaPermission }?.let { prompt ->
                            ConversationMediaPromptModel_()
                                .id(prompt.id)
                                .buttonVisible(true)
                                .onAllowButtonClick { _ -> inboxService.acceptConversationMediaPrompt(conversationId, prompt.id) }
                                .onNoButtonClick { _ -> inboxService.rejectConversationMediaPrompt(conversationId, prompt.id) }
                                .promptText(prompt.getPromptText(requireContext()))
                                .addTo(this)
                        }
                    }
                }
            } else {
                binding.ervMediaPrompts.visibility = View.GONE
            }
        })
    }

    private fun checkToShowIceBreaker() {
        hideIceBreakerMessage()
        val isEnableIceBreaker = isMessageEmpty()
        if (isEnableIceBreaker && inputBar?.isGifPreviewShowing != true) {
            if (isIcebreakerGeneratorEnabled) {
                showIceBreaker()
            } else {
                if (TwineSessionManager.getInstance().iceBreakMessages.isNotEmpty()) {
                    initIceBreaker(TwineSessionManager.getInstance().iceBreakMessages)
                } else {
                    AppRepository.getInstance().iceBreakerMessages
                        .`as`(AutoDispose.autoDisposable(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
                        .subscribe({ iceBreakMessages: List<IceBreakMessage> ->
                            TwineSessionManager.getInstance().iceBreakMessages = iceBreakMessages
                            initIceBreaker(iceBreakMessages)
                        }, { t: Throwable? -> Timber.e(t) })
                }
            }
        }
    }

    private fun isMessageEmpty(): Boolean =
        canSendMessage() && (conversation?.haveInteracted()?.not() ?: true)

    private fun initIceBreaker(iceBreakMessages: List<IceBreakMessage>) {
        getActivityContext { activity: FragmentActivity? ->
            if (iceBreakerAdapter == null) {
                myUser?.let {
                    // init adapter
                    val friendUserName = friendUserName
                    iceBreakerAdapter = IceBreakerAdapter(friendUserName)
                    iceBreakerAdapter?.setListener(this)
                    val linearLayoutManager = LinearLayoutManager(activity, LinearLayoutManager.HORIZONTAL, false)
                    binding.recyclerIceBreaker.layoutManager = linearLayoutManager
                    binding.recyclerIceBreaker.adapter = iceBreakerAdapter
                    iceBreakerAdapter?.refreshData(iceBreakMessages)

                    // text name
                    val builder = formatSpanString(
                        String.format(Locale.US, getString(R.string.tw_ice_break_name), it.name), Typeface.DEFAULT_BOLD, it.name)
                    binding.tvTextIceBreakerName.setText(builder, TextView.BufferType.SPANNABLE)
                    binding.layoutIceBreakerName.setOnClickListener { _: View? -> animateLayoutIceBreaker() }

                    // outside click
                    binding.touchView.setOnClickListener { _ ->
                        if (binding.btnGenerateIcebreaker.isVisible) {
                            binding.btnGenerateIcebreaker.performClick()
                        } else if (binding.layoutIceBreakerContent.isVisible) {
                            binding.layoutIceBreakerContent.animate().translationY(binding.recyclerIceBreaker.height.toFloat()).setDuration(300).start()
                            binding.imgArrow.animate().rotation(0f).setDuration(300).start()
                            binding.rvMessages.animate().translationY(binding.recyclerIceBreaker.height.toFloat()).setDuration(300).start()
                        }
                    }
                }
            }
            // first animation
            binding.recyclerIceBreaker.viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    if (binding.recyclerIceBreaker.height != 0) {
                        binding.recyclerIceBreaker.viewTreeObserver.removeOnGlobalLayoutListener(this)
                        binding.layoutIceBreakerContent.translationY = binding.recyclerIceBreaker.height.toFloat()
                        animateLayoutIceBreaker()
                    }
                }
            })
            showIceBreaker()
        }
    }

    private fun showIceBreaker() {
        if (isIcebreakerGeneratorEnabled) {
            binding.btnGenerateIcebreaker.visibility = View.VISIBLE
        } else {
            binding.touchView.visibility = View.VISIBLE
            binding.layoutIceBreakerContent.visibility = View.VISIBLE
        }
    }

    private fun animateLayoutIceBreaker() {
        if (isIceBreakerAnimating) {
            return
        }

        isIceBreakerAnimating = true
        binding.recyclerIceBreaker.visibility = View.VISIBLE
        var destinationHeight = 0
        var destinationDegree = 180

        if (binding.layoutIceBreakerContent.translationY == 0f) {
            destinationHeight = binding.recyclerIceBreaker.height
            destinationDegree = 0
        }
        binding.layoutIceBreakerContent.animate().translationY(destinationHeight.toFloat()).setDuration(300).start()
        binding.imgArrow.animate().rotation(destinationDegree.toFloat()).setDuration(300).start()
        binding.rvMessages.animate().translationY(destinationHeight.toFloat()).setDuration(300).setListener(
            object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    isIceBreakerAnimating = false
                }
            }
        ).start()
    }

    private val friendUserName: String
        get() {
            val inboxName = TwineApplication.instance.getInboxService()?.getFriendInboxUser(conversation)?.name.takeUnless { it.isNullOrBlank() }
            val baseName = inboxName ?: targetName.takeUnless { it.isNullOrBlank() } ?: ""
            return if (conversation?.shouldHideFriendInfo() == true) {
                censorName(baseName)
            } else {
                baseName
            }
        }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: InboxGetMoreMessageEvent) {
        conversation?.let {
            if (event.conversationId == it.id) {
                if (event.status.equals(InboxGetMoreMessageEvent.SUCCESS, ignoreCase = true)) {
                    postAndNotifyAdapter()
                }
                isStopLoadMoreMessage = event.isStopLoadMore
                isLoadedAllMessages = isNullOrEmpty(event.messages)
                binding.progressBar.animate().alpha(0.0f).setDuration(200).start()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: UnblockUserEvent) {
        if (event.status.equals(UnblockUserEvent.UNBLOCK_USER_SUCCESS, ignoreCase = true)) {
            updateConversationStatus()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: InboxConversationSeenEvent) {
        safeLet3(conversation, messagesAdapter, conversation?.seenTimestamp, { conversation, messagesAdapter, _ ->
            if (conversation.id == event.conversationId) {
                val messageSeenId = currentSeenMessageId
                if (messageSeenId <= 0) {
                    return
                }

                var count = 1 + (if (lastSeenMessageId > 0) 1 else 0)
                for (i in messagesAdapter.itemCount - 1 downTo 0) {
                    val message = messagesAdapter.getItem(i)
                    if (message != null && ((message.id == messageSeenId) || message.id == lastSeenMessageId)) {
                        messagesAdapter.notifyItemChanged(i)
                        count--

                        if (count <= 0) {
                            break
                        }
                    }
                }
                lastSeenMessageId = messageSeenId
            }
        })
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: InboxRewindEncounterLikeEvent) {
        conversation?.let {
            if (it.id == event.conversationId && it.isSourceEncounter) {
                updateEncounterStatus()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: InboxConversationMediaChangedEvent) {
        conversation?.let {
            if (it.id == event.conversationId) {
                if (event.conversationPromptId != null) {
                    checkToShowConversationPrompts()
                }
                if (event.isMediaSettingChanged) {
                    messagesAdapter?.notifyConversationMediaChanged()
                }
            }
        }
    }

    private val currentSeenMessageId: Long
        get() {
            safeLet2(myUser, conversation, { myUser, conversation ->
                if (myUser.isNormalAccount) {
                    return 0
                }
                conversation.seenTimestamp?.let {
                    for (time in it) {
                        if (time.inboxUserId != myUser.inboxUserId) {
                            return time.seenMessageId
                        }
                    }
                }
            })
            return 0
        }

    @SuppressLint("CheckResult")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: NewConversationEvent) { // handle sending message to non-exist conversation that will create new conversation
        getActivityContext { activity: FragmentActivity ->
            (activity as BaseTwineActivity).hideLoading()
            val conv = conversation
            if (conv?.id == event.conversationId) {
                postAndNotifyAdapter()
                return@getActivityContext
            }
            if (event.conversationId != conversationId) {
                return@getActivityContext
            }
            val inboxService = TwineApplication.instance.getInboxService()
            if (inboxService == null || !InboxBaseEvent.SUCCESS.equals(event.status, ignoreCase = true)) {
                activity.finish()
                return@getActivityContext
            }

            Single.fromCallable { inboxService.getLocalConversation(conversationId) ?: InboxConversation() }
                .compose(SchedulerUtils.ioToMain())
                .`as`(
                    AutoDispose.autoDisposable(
                        AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)
                    )
                )
                .subscribe({ newConv ->
                    if (newConv?.id != 0) {
                        if (conv == null && newConv.isSourceEncounter) {
                            TrackingUtil.logEncounterConversationOpened(encounterTrackingType)
                        }
                        this.conversation = newConv
                        conversationId = newConv.id
                        myInboxUser = inboxService.getMyInboxUser(newConv)
                        friendInboxUser = inboxService.getFriendInboxUser(newConv)
                        initUI()
                        updateConversationStatus()
                        binding.rvMessages.visibility = View.VISIBLE
                    }
                }, { error ->
                    Log.ePrintLn(error)
                })
        }
    }

    override fun onFlashChatDone(position: Int) {
        if (!binding.rvMessages.isComputingLayout) {
            if (inboxFlashMessagePreviewDialogFragment?.isVisible == false) {
                removeFlashChatItem(position)
            }
        }
    }

    private fun removeFlashChatItem(position: Int) {
        messagesAdapter?.let {
            if (position >= 0 && position < it.itemCount) {
                val inboxMessage = it.getItem(position)
                if (inboxMessage != null && conversation != null && inboxMessage.isViewingFlashMessage) {
                    it.notifyItemChanged(position)
                    // update preview position for detect avatar position changed or not.
                    for (i in position - 1 downTo 0) {
                        if (it.getItemViewType(i) != InboxMessage.MINGLE_MESSAGE_TYPE_EMPTY) {
                            it.notifyItemChanged(i)
                            break
                        }
                    }
                }
            }
        }
        checkToShowPowerAccountAd()
        checkToShowIceBreaker()
        checkToUpdateInputBarStatus()
    }

    override fun onInputBarReady(stickerInputBar: StickerInputBar) {
        if (inputBar == null) {
            getActivityContext { activity: FragmentActivity? ->
                inputBar = stickerInputBar
                inputBar?.setFragmentManager(childFragmentManager)
                inputBar?.setInputBarActionHandler(mInputBarActionHandler)
                inputBar?.setEnterKeyToSend(myUser?.userSetting?.isEnterToSend ?: false)
                activity?.let {
                    inputBar?.setFlashColor(ContextCompat.getColor(it, R.color.colorStickerFlash))
                    inputBar?.setIconColor(ContextCompat.getColor(it, R.color.tw_inputbar_color_unselected))
                    inputBar?.setActiveColor(ContextCompat.getColor(it, R.color.tw_primaryColor))
                }
                inputBar?.isEnabled = true
                inputBar?.setEnableText(true)
                inputBar?.setEnableAudio(true)
                inputBar?.disableChat("")
                if (conversation != null) {
                    inputBar?.setEnablePhotoAndVideo(true)
                    updateConversationAfterInitialize()
                } else {
                    inputBar?.setEnablePhotoAndVideo(false)
                }
            }
        }
    }

    private fun updateConversationAfterInitialize() {
        conversation?.let {
            if (ConversationStatus.LEFT.equals(it.status, ignoreCase = true)) {
                inputBar?.disableChat(getString(R.string.tw_inbox_conversation_has_left))
                postAndNotifyAdapter()
                binding.rvMessages.visibility = View.VISIBLE
            } else {
                TwineApplication.instance.getInboxService()?.getLatestMessages(it.id)
            }
        }
        setConversationRead()
        updateConversationStatus()
        checkToShowConversationSourceAndSafetyTip(true)
    }

    private fun clearInputBarContent() {
        if (inputBar?.editTextMessage != null) {
            inputBar?.clearContent()
        }
    }

    override fun onIceBreakerSelected(message: IceBreakMessage, formattedMessage: String?) {
        val inboxMessage = InboxMessage()
        myUser?.let { inboxMessage.inboxUserId = it.inboxUserId }
        conversation?.let { inboxMessage.conversationId = it.id }
        myInboxUser?.let { inboxMessage.user = it }
        inboxMessage.isLocal = true
        inboxMessage.content = formattedMessage
        sendInboxMessage(inboxMessage)
        TrackingUtil.logIceBreakerSent(message.id.toLong())

        hideIceBreakerMessage()
        binding.rvMessages.translationY = 0f
    }

    private val inputBarResultLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result: ActivityResult ->
        if (result.resultCode == Activity.RESULT_OK) {
            inputBar?.handleActivityResult(result.resultCode, result.data)
        }
    }

    override fun registerCameraResultLauncher(intent: Intent) {
        inputBarResultLauncher.launch(intent)
    }

    override fun onMediaClick(event: MediaClickEvent) {
        if (event.item != null && event.item.uri != null) {
            getActivityContext { activity: FragmentActivity? ->
                Single
                    .fromCallable {
                        val intent = Intent(activity, MediaPreviewActivity::class.java)
                        intent.putExtra(MediaPreviewActivity.BUNDLE_PHOTO_PATH, if (event.item.isImage) PathUtils.getPath(activity, event.item.contentUri) else null)
                        intent.putExtra(MediaPreviewActivity.BUNDLE_VIDEO_PATH, if (event.item.isVideo) PathUtils.getPath(activity, event.item.contentUri) else null)
                        intent.putExtra(MediaPreviewActivity.BUNDLE_ENABLE_AUTO_DELETE, inputBar?.enableAutoDelete)
                        intent
                    }.subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                    .`as`(
                        AutoDispose.autoDisposable(
                            AndroidLifecycleScopeProvider.from(
                                this,
                                Lifecycle.Event.ON_DESTROY
                            )
                        )
                    )
                    .subscribe(
                        { input: Intent -> inputBarResultLauncher.launch(input) },
                        { obj: Throwable -> obj.printStackTrace() })
            }
        }
    }

    override fun onCapture() {
        mInputBarActionHandler.openCamera()
    }

    // handle getting conversation if exist
    private val inboxGetConversationCallback: InboxGetConversationCallback = object : InboxGetConversationCallback {
        override fun onSuccess(conversation: InboxConversation) {
            getActivityContext { activity: FragmentActivity ->
                activity.runOnUiThread {
                    (activity as BaseTwineActivity).hideLoading()
                    <EMAIL> = conversation
                    TwineApplication.instance.getInboxService()?.let {
                        conversationId = conversation.id
                        myInboxUser = it.getMyInboxUser(conversation)
                        friendInboxUser = it.getFriendInboxUser(conversation)
                    }
                    initUI()
                    updateConversationStatus()
                    binding.rvMessages.visibility = View.VISIBLE
                    if (conversation.isSourceEncounter) {
                        TrackingUtil.logEncounterConversationOpened(encounterTrackingType)
                    }
                }
            }
        }

        override fun onError(throwable: Throwable) {
            getActivityContext { activity: FragmentActivity ->
                activity.runOnUiThread {
                    (activity as BaseTwineActivity).hideLoading()
                    checkToShowCanSendMessage()
                    checkToShowIceBreaker()
                    checkToShowPowerAccountAd()
                    checkToUpdateInputBarStatus()
                    updateConversationStatus()
                }
            }
        }
    }

    private fun handleCanSendMessageResponse(receiverId: Int, canSendMessageResponse: CanSendMessageResponse?, throwable: Throwable?) {
        if (canSendMessageResponse != null) {
            canSendMessageWrapper = CanSendMessageWrapper(canSendMessageResponse)
        } else if (throwable != null) {
            if (throwable is HttpException) {
                val error = TwineApplication.instance.retrofitHelper?.convertToBaseError(throwable)
                if (error != null && BaseError.ACCOUNT_NOT_FOUND_TYPE == error.errorType) {
                    val accountNotFoundResponse = CanSendMessageResponse()
                    accountNotFoundResponse.isPermit = false
                    accountNotFoundResponse.reasons = ArrayList(listOf(BaseError.ACCOUNT_NOT_FOUND_TYPE))
                    canSendMessageWrapper = CanSendMessageWrapper(accountNotFoundResponse)
                } else {
                    getActivityContext { activity: FragmentActivity ->
                        showToastSafe(activity, throwable.message)
                        activity.finish()
                    }
                    return
                }
            } else {
                getActivityContext { activity: FragmentActivity ->
                    showToastSafe(activity, throwable.message)
                    activity.finish()
                }
                return
            }
        }
        if (conversation != null) {
            val activity: Activity? = activity
            if (activity is BaseTwineActivity) {
                activity.hideLoading()
            }
            initUI()
            updateConversationStatus()
        } else {
            val inboxService = TwineApplication.instance.getInboxService()
            inboxService?.getConversationByInboxUserId(receiverId, inboxGetConversationCallback)
        }
    }

    fun onFriendFeedUserUpdated(friendFeedUser: FeedUser?) {
        this.friendFeedUser = friendFeedUser
        checkToShowConversationSourceAndSafetyTip(true)
        checkToShowCanSendMessage()
    }

    private fun checkToShowCanSendMessage() {
        safeLet3(myUser, canSendMessageWrapper, friendFeedUser, { myUser, canSendMessageWrapper, friendFeedUser ->
            if (canSendMessageWrapper.isSentReminded) {
                //Only display layout reminded for user not match yet
                val isMatched = (friendFeedUser.isMatched || myUser.isMatchedWithId(friendFeedUser.id))
                setLayoutRemindedEnable(!isMatched)
                setReplyReminderEnabled(false)
                setVerifiedEnabled(canSendMessageWrapper.isRequireVerifyPhoto)
            } else if (canSendMessageWrapper.isNeedToReplyReminded) {
                setLayoutRemindedEnable(false)
                setReplyReminderEnabled(true)
                setVerifiedEnabled(false)
            } else {
                setLayoutRemindedEnable(false)
                setReplyReminderEnabled(false)
                setVerifiedEnabled(canSendMessageWrapper.isRequireVerifyPhoto)
            }
            setInputBarEnabled(canSendMessage())
        })
    }

    private fun setLayoutRemindedEnable(enable: Boolean) {
        if (enable) {
            if (!binding.vsLayoutReminded.isInflated) {
                binding.vsLayoutReminded.viewStub?.inflate()
            }
            binding.vsLayoutReminded.root.visibility = View.VISIBLE
            safeLet2(friendFeedUser, binding.vsLayoutReminded.binding as LayoutInboxRemindedBinding?, { friendFeedUser, inboxRemindedBinding ->
                if (User.MALE.equals(friendFeedUser.gender, ignoreCase = true)) {
                    inboxRemindedBinding.tvReminded.text = String.format(
                        Locale.US, getString(R.string.tw_remind_inbox_notice_sent),
                        getString(R.string.tw_say_hi_confirm_dialog_he), getString(R.string.tw_gender_he)
                    )
                } else {
                    inboxRemindedBinding.tvReminded.text = String.format(
                        Locale.US, getString(R.string.tw_remind_inbox_notice_sent),
                        getString(R.string.tw_say_hi_confirm_dialog_she), getString(R.string.tw_gender_she)
                    )
                }
            })
        } else {
            if (binding.vsLayoutReminded.isInflated) {
                binding.vsLayoutReminded.root.visibility = View.GONE
            }
        }
    }

    private fun setReplyReminderEnabled(enabled: Boolean) {
        if (enabled) {
            friendFeedUser?.let {
                val gender = if (User.MALE.equals(it.gender, ignoreCase = true)) {
                    getString(R.string.tw_say_hi_confirm_dialog_he)
                } else {
                    getString(R.string.tw_say_hi_confirm_dialog_she)
                }
                binding.tvReplyReminderTitle.text =
                    String.format(Locale.US, getString(R.string.tw_remind_inbox_notice_received), friendUserName, gender)
                binding.layoutReplyReminder.visibility = View.VISIBLE
            }
        } else {
            binding.layoutReplyReminder.visibility = View.GONE
        }
    }

    private fun setInputBarEnabled(enabled: Boolean) {
        if (enabled) {
            inputBar?.enableChat()
        } else {
            inputBar?.disableChat("")
        }
    }

    private fun containsMatchedMessage(): Boolean {
        conversation?.messages?.let {
            if (it.isNotEmpty()) {
                for (message in it) {
                    if (message.user == null &&
                        (Constants.YOU_TWO_HAVE_BEEN_MATCHED.equals(message.content, ignoreCase = true)
                                || Constants.YOU_HAVE_FOUND_YOU_SOUL_MATCH.equals(message.content, ignoreCase = true))
                    ) {
                        return true
                    }
                }
            }
        }
        return false
    }

    private val isEnableAutoDelete: Boolean
        get() = (myUser?.isNormalAccount == false) || (canSendMessage() && conversation?.haveInteracted() == true)

    private fun canSendMessage(): Boolean {
        safeLet2(myUser, canSendMessageWrapper, { myUser, canSendMessageWrapper ->
            return !canSendMessageWrapper.isDeactivated && !canSendMessageWrapper.isRequireVerifyPhoto && (!myUser.isNormalAccount || canSendMessageWrapper.canSendMessage())
        })
        return false
    }

    private class CanSendMessageWrapper(private val canSendMessageResponse: CanSendMessageResponse?) {
        fun canSendMessage(): Boolean {
            return canSendMessageResponse != null && canSendMessageResponse.isPermit
        }

        val isSentReminded: Boolean
            get() = canSendMessageResponse != null && canSendMessageResponse.reasons != null && canSendMessageResponse.reasons
                .contains(REASON_SENT_REMINDED)

        val isNeedToReplyReminded: Boolean
            get() = canSendMessageResponse != null && canSendMessageResponse.reasons != null && canSendMessageResponse.reasons
                .contains(REASON_NEED_TO_REPLY_REMINDED)

        val isRequireVerifyPhoto: Boolean
            get() = canSendMessageResponse != null && canSendMessageResponse.reasons != null && canSendMessageResponse.reasons
                .contains(REASON_VERIFY_PHOTO)

        val isDeactivated: Boolean
            get() = canSendMessageResponse != null && canSendMessageResponse.reasons != null && canSendMessageResponse.reasons.contains(BaseError.ACCOUNT_NOT_FOUND_TYPE)

        fun onMatched() {
            if (canSendMessageResponse != null) {
                val reasons = canSendMessageResponse.reasons
                if (reasons != null) {
                    reasons.remove(REASON_SENT_REMINDED)
                    reasons.remove(REASON_NEED_TO_REPLY_REMINDED)
                }
                canSendMessageResponse.isPermit = isNullOrEmpty(reasons)
            }
        }

        fun onPhotoVerified() {
            if (canSendMessageResponse != null) {
                val reasons = canSendMessageResponse.reasons
                reasons?.remove(REASON_VERIFY_PHOTO)
                canSendMessageResponse.isPermit = isNullOrEmpty(reasons)
            }
        }

        fun onPowerAccountUpdated(myUser: User?) {
            myUser?.let {
                if (!it.isNormalAccount) {
                    canSendMessageResponse?.reasons?.apply {
                        remove(REASON_SENT_REMINDED)
                        remove(REASON_NEED_TO_REPLY_REMINDED)
                    }
                    canSendMessageResponse?.isPermit = canSendMessageResponse?.reasons.isNullOrEmpty()
                }
            }
        }

        companion object {
            private const val REASON_VERIFY_PHOTO = "require_verified_partner"
            private const val REASON_SENT_REMINDED = "already_sent_reminded_request"
            private const val REASON_NEED_TO_REPLY_REMINDED = "need_to_reply_reminded_request"

            fun create(reason: String): CanSendMessageWrapper {
                val canSendMessageResponse = CanSendMessageResponse()
                val reasons = ArrayList<String>()
                if (REASON_SENT_REMINDED.equals(reason, ignoreCase = true)
                    || REASON_NEED_TO_REPLY_REMINDED.equals(reason, ignoreCase = true)
                    || REASON_VERIFY_PHOTO.equals(reason, ignoreCase = true)
                ) {
                    canSendMessageResponse.isPermit = false
                    reasons.add(reason)
                }
                canSendMessageResponse.reasons = reasons
                return CanSendMessageWrapper(canSendMessageResponse)
            }
        }
    }

    companion object {
        private const val TIME_AFTER_VIEW_AUTO_DELETE = 1000

        private const val CONVERSATION_ID = "conversation_id"
        private const val TARGET_INBOX_USER_ID = "target_inbox_user_id"
        private const val TARGET_NAME = "target_name"
        private const val IS_DIRECT_CHAT_BY_AD = "is_direct_chat_by_ad"

        @JvmStatic
        fun newInstance(conversationId: Int, friendFeedUser: FeedUser?): InboxConversationFragment {
            val fragment = InboxConversationFragment()
            val args = Bundle()
            args.putInt(CONVERSATION_ID, conversationId)
            fragment.arguments = args
            fragment.setFriendFeedUser(friendFeedUser)
            return fragment
        }

        @JvmStatic
        fun newInstanceWithInboxUserId(
            targetInboxUserId: Int, targetName: String?,
            friendFeedUser: FeedUser?, isDirectChatByAd: Boolean
        ): InboxConversationFragment {
            val fragment = InboxConversationFragment()
            val args = Bundle()
            args.putInt(TARGET_INBOX_USER_ID, targetInboxUserId)
            args.putString(TARGET_NAME, targetName)
            args.putBoolean(IS_DIRECT_CHAT_BY_AD, isDirectChatByAd)
            fragment.arguments = args
            fragment.setFriendFeedUser(friendFeedUser)
            return fragment
        }
    }
}
