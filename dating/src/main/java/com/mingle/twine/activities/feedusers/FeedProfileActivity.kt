package com.mingle.twine.activities.feedusers

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.core.app.ActivityCompat
import androidx.core.app.ActivityOptionsCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Lifecycle
import com.mingle.global.utils.ArrayUtil
import com.mingle.inbox.model.eventbus.net.InboxBaseEvent
import com.mingle.inbox.model.eventbus.net.InboxUnblockUserEvent
import com.mingle.twine.R
import com.mingle.twine.activities.BaseTwineActivity
import com.mingle.twine.activities.inbox.InboxConversationActivity
import com.mingle.twine.data.UserDataManager.canPlaySayHiAnimation
import com.mingle.twine.data.UserDataManager.user
import com.mingle.twine.data.UserSharedPrefs
import com.mingle.twine.databinding.ActivityFeedProfileBinding
import com.mingle.twine.fragments.dialog.OutOfCoinDialog
import com.mingle.twine.fragments.dialog.SayHiConfirmDialogFragment.SayHiConfirmDialogFragmentInteraction
import com.mingle.twine.fragments.dialog.report.ReportUserDialogFragment.Companion.openReportUserDialog
import com.mingle.twine.fragments.feedusers.FeedProfileBottomSheetFragment.ProfileBottomSheetInteractionListener
import com.mingle.twine.fragments.feedusers.FeedProfileFragment
import com.mingle.twine.models.FeedUser
import com.mingle.twine.models.TwineConstants
import com.mingle.twine.models.eventbus.FeedUserChangeEvent
import com.mingle.twine.models.eventbus.RemoveFeedUserEvent
import com.mingle.twine.models.eventbus.UserReloadedEvent
import com.mingle.twine.repository.AppRepository
import com.mingle.twine.utils.SingleDebounceClickListener
import com.mingle.twine.utils.TwineDialogHelper
import com.mingle.twine.utils.scheduler.SchedulerUtils
import com.mingle.twine.utils.tracking.TrackingParams
import com.mingle.twine.utils.tracking.TrackingScreenNames
import com.mingle.twine.utils.tracking.TrackingUtil
import com.uber.autodispose.AutoDispose
import com.uber.autodispose.SingleSubscribeProxy
import com.uber.autodispose.android.lifecycle.AndroidLifecycleScopeProvider
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class FeedProfileActivity : BaseTwineActivity(), ProfileBottomSheetInteractionListener {

    private lateinit var binding: ActivityFeedProfileBinding
    private var feedUser: FeedUser? = null
    private var trackingScreenName: String? = null
    private var feedProfileFragment: FeedProfileFragment? = null

    @SuppressLint("ClickableViewAccessibility")
    override fun onCreateImpl(savedInstanceState: Bundle?) {
        binding = DataBindingUtil.setContentView(this@FeedProfileActivity, R.layout.activity_feed_profile)
        binding.background.setOnClickListener(clickListener)
        binding.btnMsg.setOnClickListener(clickListener)
        binding.btnHeart.setOnClickListener(clickListener)
        binding.btnHi.setOnClickListener(clickListener)
        loadData()
    }

    val feedUserId: Int
        get() = if (feedUser != null) feedUser!!.id else -1

    private fun loadData() {
        binding.pbLoading.visibility = View.VISIBLE
        val userId = this.intent.getIntExtra(USER_ID, -1)
        trackingScreenName = intent.getStringExtra(TRACKING_SCREEN_NAME)
        if (userId != -1) {
            AppRepository
                    .getInstance()
                    .getUserProfileByUserId(listOf(userId.toString()), true)
                    .compose(SchedulerUtils.ioToMain())
                    .`as`<SingleSubscribeProxy<ArrayList<FeedUser?>>>(AutoDispose.autoDisposable(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
                    .subscribe { feedUsers: ArrayList<FeedUser?> ->
                        if (feedUsers.isNotEmpty()) {
                            feedUser = feedUsers[0]
                        }
                        displayFeedProfileFragment(feedUser)
                    }
        } else {
            displayFeedProfileFragment(feedUser)
        }
    }

    private fun displayFeedProfileFragment(feedUser: FeedUser?) {
        feedUser?.let {
            binding.layoutControls.visibility = View.VISIBLE
            updateLikeHiAndMessageStatus()
        }
        feedProfileFragment = FeedProfileFragment()
        feedProfileFragment?.setData(feedUser, trackingScreenName)
        feedProfileFragment?.let {
            it.setCanPlayVideo(true)
            it.fragmentVisibilityListener = object : FeedProfileFragment.FragmentVisibilityListener {
                override fun onFragmentVisible() {
                    binding.pbLoading.visibility = View.GONE
                }

                override fun onFragmentHidden() {}
            }
            supportFragmentManager.beginTransaction()
                    .add(R.id.container, it, FeedProfileFragment::class.java.simpleName).commitAllowingStateLoss()
        }
    }

    private fun updateLikeHiAndMessageStatus() {
        feedUser?.let {
            if (it.isMatched || it.isLiked) {
                binding.btnHeart.setImageResource(R.drawable.ic_heart_active)
            } else {
                binding.btnHeart.setImageResource(R.drawable.ic_heart_inactive)
            }
            if (it.isSaidHi) {
                binding.btnHeart.setImageResource(R.drawable.ic_heart_active)
            }
            feedProfileFragment?.updateHiStatus(it.isSaidHi)
        }
    }

    private val clickListener: SingleDebounceClickListener = object : SingleDebounceClickListener(300) {
        override fun onClickImpl(v: View?) {
            if (v == binding.background) {
                finish()
            } else if (v === binding.btnMsg) {
                onMessageInteraction()
            } else if (v === binding.btnHeart) {
                onLikeInteraction()
            } else if (v === binding.btnHi) {
                onSayHiInteraction()
            }
        }
    }

    fun onLikeInteraction() {
        val user = user
        if (user == null || feedUser == null) {
            return
        }
        if (feedUser!!.isLiked) {
            // already liked
            return
        }
        if (user.hasMedia() || user.channelSetting?.isForcePrimaryMediaToLike == false) {
            if (ArrayUtil.arrayContains(user.blockedUserIds, feedUser!!.id)) {
                TwineDialogHelper.showUnblockRequiredConfirmDialog(this@FeedProfileActivity, "",
                        getString(R.string.tw_unblock_required),
                        { unblockUser(feedUser!!.id, feedUser!!.inboxUserId) }, null)
            } else {
                TrackingScreenNames.sendHiLikeFromScreen = trackingScreenName!!
                val params: MutableMap<String, Any> = HashMap()
                params[TwineConstants.PARAM_LIKE_SOURCE] = TwineConstants.LIKE_SOURCE_LIKE
                sendLikeToUser(feedUser, params, FeedUserChangeEvent.ALL_SCREEN)
                binding.btnHeart.setImageResource(R.drawable.ic_heart_active)
            }
        } else {
            showUploadDialog()
        }
    }

    fun onSayHiInteraction() {
        val user = user
        if (user == null || feedUser == null) {
            return
        }
        if (user.hasMedia()) {
            if (user.credits >= (user.channelSetting?.spendCreditRules?.sendCharm ?: 0)) {
                if (ArrayUtil.arrayContains(user.blockedUserIds, feedUser!!.id)) {
                    TwineDialogHelper.showUnblockRequiredConfirmDialog(this@FeedProfileActivity,
                            "", getString(R.string.tw_unblock_required),
                            { unblockUser(feedUser!!.id, feedUser!!.inboxUserId) }, null)
                } else if (UserSharedPrefs.getInstance().getDisplaySayHiConfirmDialogTime(
                                applicationContext) < TwineConstants.MAX_TIME_DISPLAY_SAY_HI_CONFIRM_DIALOG) {
                    UserSharedPrefs.getInstance().setDisplaySayHiConfirmDialogTime(applicationContext,
                            UserSharedPrefs.getInstance().getDisplaySayHiConfirmDialogTime(applicationContext) + 1)
                    TwineDialogHelper.showSayHiConfirmDialog(this@FeedProfileActivity,
                            object : SayHiConfirmDialogFragmentInteraction {
                                override fun onYayClick() {
                                    onSenHiToUser(feedUser!!, SEND_HI_BY_COIN)
                                }

                                override fun onCancelClick() {}
                            })
                } else {
                    onSenHiToUser(feedUser!!, SEND_HI_BY_COIN)
                }
            } else {
                TwineDialogHelper.showOutOfCoinDialog(this, TrackingParams.CAUSE_SAY_HI, object : OutOfCoinDialog.Callback {
                    override fun onSayHiAdViewed() {
                        onSenHiToUser(feedUser!!, SEND_HI_BY_ADS)
                    }
                })
            }
        } else {
            showUploadDialog()
        }
    }

    fun onSenHiToUser(feedUser: FeedUser, sendHiBy: String?) {
        TrackingScreenNames.sendHiLikeFromScreen = trackingScreenName!!
        sendHiToUser(feedUser, sendHiBy, FeedUserChangeEvent.ALL_SCREEN)
        if (canPlaySayHiAnimation(feedUser.id)) {
            binding.lottieHiAnimation.playAnimation()
            feedProfileFragment?.updateHiStatus(true)
            binding.btnHeart.setImageResource(R.drawable.ic_heart_active)
        }
    }

    private fun onMessageInteraction() {
        val user = user
        if (user == null || feedUser == null) {
            return
        }
        feedUser?.let {
            InboxConversationActivity.checkToOpenConversation(
                this, it.id, it.inboxUserId, it.name, it.smallPrimaryMedia, it.gender, it.isVerified, true, trackingScreenName)
        }
    }

    fun notifyNewMatch(userId: Int, inboxUserId: Int) {
        feedUser?.let {
            if (it.id == userId && it.inboxUserId == inboxUserId) {
                it.isMatched = true
                updateLikeHiAndMessageStatus()
            }
        }
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, R.anim.dialog_slide_down)
    }

    override fun onReport() {
        openReportUserDialog(this, feedUser!!.id, feedUser!!.inboxUserId, feedUser!!.name)
    }

    override fun onBlockUnblock() {
        if (feedUser != null) {
            onBlockUnblock(feedUser)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(@Suppress("UNUSED_PARAMETER") event: UserReloadedEvent?) {
        val user = user
        if (user != null && user.isDirectChatEnabled) {
            binding.btnMsg.setImageResource(R.drawable.ic_message_active)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: RemoveFeedUserEvent) {
        if (feedUser != null && feedUser!!.id == event.feedUserId) {
            finish()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: InboxUnblockUserEvent) {
        if (InboxBaseEvent.SUCCESS == event.status) {
            updateLikeHiAndMessageStatus()
        }
    }

    companion object {
        const val USER_ID = "user_id"
        const val TRACKING_SCREEN_NAME = "TRACKING_SCREEN_NAME"

        @JvmStatic
        fun start(context: Context, feedUserId: Int, trackingScreenName: String?) {
            val intent = Intent(context, FeedProfileActivity::class.java)
            intent.putExtra(USER_ID, feedUserId)
            intent.putExtra(TRACKING_SCREEN_NAME, trackingScreenName)
            try {
                ActivityCompat.startActivity(context, intent, ActivityOptionsCompat.makeCustomAnimation(context, R.anim.dialog_slide_up, 0).toBundle())
            } catch (e: Exception) {
                context.startActivity(intent)
            }
            TrackingUtil.logViewProfileEvent(trackingScreenName)
        }
    }
}