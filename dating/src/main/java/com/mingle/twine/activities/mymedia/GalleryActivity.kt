package com.mingle.twine.activities.mymedia

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.Window
import android.view.WindowManager
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import com.mingle.global.utils.PathUtils
import com.mingle.sticker.widget.StickerInputBar
import com.mingle.twine.R
import com.mingle.twine.activities.BaseTwineActivity
import com.mingle.twine.gallery.ui.MediaPreviewActivity
import com.mingle.twine.gallery.ui.PhotoFragment
import com.mingle.twine.gallery.ui.adapter.AlbumMediaAdapter
import com.mingle.twine.models.TwineConstants
import com.mingle.twine.utils.AppPermissionUtils
import com.mingle.twine.utils.AppPermissionUtils.RequestPermissionCallback
import com.mingle.twine.utils.TwineDialogHelper
import com.mingle.twine.utils.TwineUtils
import com.mingle.twine.utils.TwineUtils.generateRandomName
import com.uber.autodispose.AutoDispose
import com.uber.autodispose.android.lifecycle.AndroidLifecycleScopeProvider
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers

class GalleryActivity : BaseTwineActivity() {

    companion object {
        private const val REQUEST_MEDIA_PERMISSION = 0

        private const val KEY_ENABLE_PHOTO = "KEY_ENABLE_PHOTO"
        private const val KEY_ENABLE_VIDEO = "KEY_ENABLE_VIDEO"

        @JvmStatic
        fun newIntent(context: Context, enablePhoto: Boolean, enableVideo: Boolean, enablePreview: Boolean, enableAutoDelete: Boolean): Intent {
            val intent = Intent(context, GalleryActivity::class.java)
            intent.putExtra(KEY_ENABLE_PHOTO, enablePhoto)
            intent.putExtra(KEY_ENABLE_VIDEO, enableVideo)
            intent.putExtra(CameraActivity.ARG_ENABLE_PREVIEW, enablePreview)
            intent.putExtra(CameraActivity.ARG_ENABLE_AUTO_DELETE, enableAutoDelete)
            return intent
        }
    }

    private lateinit var mediaPreviewResultLauncher : ActivityResultLauncher<Intent>

    override fun onCreate(savedInstanceState: Bundle?) {
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        window?.setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN)
        mediaPreviewResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            val data = result.data
            if (result.resultCode == Activity.RESULT_OK && data != null) {
                val videoFilePath = data.getStringExtra(StickerInputBar.BUNDLE_VIDEO_PATH)
                val photoFilePath = data.getStringExtra(StickerInputBar.BUNDLE_PHOTO_PATH)
                val autoDelete = data.getBooleanExtra(StickerInputBar.BUNDLE_AUTO_DELETE, false)
                if (!TextUtils.isEmpty(photoFilePath)) {
                    val photoName = TwineUtils.generateRandomName() + TwineConstants.DEFAULT_PHOTO_EXTENSION
                    sendPhoto(photoName, photoFilePath, autoDelete)
                } else if (!TextUtils.isEmpty(videoFilePath)) {
                    val videoName = TwineUtils.generateRandomName() + TwineConstants.DEFAULT_VIDEO_EXTENSION
                    sendVideo(videoName, videoFilePath, autoDelete)
                }
            }
        }
        super.onCreate(savedInstanceState)
    }

    override fun onCreateImpl(savedInstanceState: Bundle?) {
        checkToShowPhotos()
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        if (requestCode == REQUEST_MEDIA_PERMISSION) {
            if (AppPermissionUtils.isPermissionsGranted(grantResults)) {
                showPhotosFragment()
            } else {
                if (grantResults.isNotEmpty()) {
                    finish()
                }
            }
        } else {
            super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        }
    }

    private fun recommendUserAllowPermissions() {
        TwineDialogHelper.showConfirmDialog(context, "", getString(R.string.tw_recommend_user_allow_permissions),
                { v: View? -> TwineUtils.startAppSettingsActivity(context) }) { v: View? -> finish()}
    }

    private fun checkToShowPhotos() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_IMAGES) != PackageManager.PERMISSION_GRANTED) {
                AppPermissionUtils.requestPermission(
                        this, Manifest.permission.READ_MEDIA_IMAGES, REQUEST_MEDIA_PERMISSION,
                        object : RequestPermissionCallback {
                            override fun onPermissionGranted() {
                                checkToShowPhotos()
                            }

                            override fun onShowRequestPermissionExplanation() {
                                recommendUserAllowPermissions()
                            }
                        })
                return
            }
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_VIDEO) != PackageManager.PERMISSION_GRANTED) {
                AppPermissionUtils.requestPermission(
                        this, Manifest.permission.READ_MEDIA_VIDEO, REQUEST_MEDIA_PERMISSION,
                        object : RequestPermissionCallback {
                            override fun onPermissionGranted() {
                                checkToShowPhotos()
                            }

                            override fun onShowRequestPermissionExplanation() {
                                recommendUserAllowPermissions()
                            }
                        })
                return
            }
            showPhotosFragment()
        } else {
            val storagePermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) Manifest.permission.READ_EXTERNAL_STORAGE else Manifest.permission.WRITE_EXTERNAL_STORAGE
            if (AppPermissionUtils.hasPermissions(this, storagePermission)) {
                showPhotosFragment()
            } else {
                AppPermissionUtils.requestPermissions(this, arrayOf(storagePermission), REQUEST_MEDIA_PERMISSION)
            }
        }
    }

    private fun showPhotosFragment() {
        val enablePhoto: Boolean = intent.extras?.getBoolean(KEY_ENABLE_PHOTO, true) ?: true
        val enableVideo: Boolean = intent.extras?.getBoolean(KEY_ENABLE_VIDEO, false) ?: false
        val enablePreview: Boolean = intent.extras?.getBoolean(CameraActivity.ARG_ENABLE_PREVIEW, false) ?: false
        val enableAutoDelete: Boolean = intent.extras?.getBoolean(CameraActivity.ARG_ENABLE_AUTO_DELETE, false) ?: false

        var photosFragment: PhotoFragment? = supportFragmentManager.findFragmentByTag(PhotoFragment::class.java.simpleName) as? PhotoFragment
        if (photosFragment == null) {
            photosFragment = PhotoFragment.newInstance(
                enableDragging = false,
                enableCapture = false, enablePhoto = enablePhoto, enableVideo = enableVideo
            )
            supportFragmentManager.beginTransaction()
                .add(android.R.id.content, photosFragment, PhotoFragment::class.java.simpleName).commitAllowingStateLoss()
            photosFragment.mOnMediaClickListener = object : PhotoFragment.OnMediaClickListener {
                override fun onMediaClick(event: AlbumMediaAdapter.MediaClickEvent) {
                    if (event.item != null && event.item.uri != null) {
                        if (enablePreview || enableAutoDelete) {
                            Single.fromCallable {
                                val intent = Intent(this@GalleryActivity, MediaPreviewActivity::class.java)
                                intent.putExtra(
                                    MediaPreviewActivity.BUNDLE_PHOTO_PATH,
                                    if (event.item.isImage) PathUtils.getPath(this@GalleryActivity, event.item.contentUri) else null
                                )
                                intent.putExtra(
                                    MediaPreviewActivity.BUNDLE_VIDEO_PATH,
                                    if (event.item.isVideo) PathUtils.getPath(this@GalleryActivity, event.item.contentUri) else null
                                )
                                intent.putExtra(MediaPreviewActivity.BUNDLE_ENABLE_AUTO_DELETE, enableAutoDelete)
                                intent
                            }.subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                                .`as`(AutoDispose.autoDisposable(AndroidLifecycleScopeProvider.from(lifecycle, Lifecycle.Event.ON_DESTROY)))
                                .subscribe({ input: Intent -> mediaPreviewResultLauncher.launch(input) }) { obj: Throwable -> obj.printStackTrace() }
                        } else {
                            val mediaFilePath = PathUtils.getPath(this@GalleryActivity, event.item.contentUri)
                            if (!mediaFilePath.isNullOrBlank()) {
                                if (event.item.isImage) {
                                    val photoName = generateRandomName() + TwineConstants.DEFAULT_PHOTO_EXTENSION
                                    sendPhoto(photoName, mediaFilePath, false);
                                } else if (event.item.isVideo) {
                                    val videoName = generateRandomName() + TwineConstants.DEFAULT_VIDEO_EXTENSION
                                    sendPhoto(videoName, mediaFilePath, false);
                                }
                            }
                        }
                    }
                }

                override fun onCapture() {}
            }
        }
    }

    fun sendPhoto(fileName: String, photoFilePath: String?, autoDelete: Boolean) {
        val intent = Intent()
        intent.putExtra(CameraActivity.BUNDLE_PHOTO_PATH, photoFilePath)
        intent.putExtra(CameraActivity.BUNDLE_FILE_NAME, fileName)
        intent.putExtra(CameraActivity.BUNDLE_AUTO_DELETE, autoDelete)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    fun sendVideo(fileName: String, videoFilePath: String?, autoDelete: Boolean) {
        val intent = Intent()
        intent.putExtra(CameraActivity.BUNDLE_VIDEO_PATH, videoFilePath)
        intent.putExtra(CameraActivity.BUNDLE_FILE_NAME, fileName)
        intent.putExtra(CameraActivity.BUNDLE_AUTO_DELETE, autoDelete)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }
}