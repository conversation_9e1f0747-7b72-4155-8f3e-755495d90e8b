import Versions.daggerVersion
import Versions.glideVersion
import Versions.navigationVersion
import Versions.preferenceVersion
import Versions.supportPercentLayoutVersion
import Versions.supportSpringAnimationVersion

object Versions {
    const val buildToolsVersion = "36.0.0"
    const val compileSdkVersion = 36
    const val minSdkVersion = 23
    const val targetSdkVersion = 35
    const val ndkVersion = "27.0.12077973"
    const val toroVersion = 1
    const val supportSpringAnimationVersion = "1.0.0"
    const val supportPercentLayoutVersion = "1.0.0"
    const val playServiceLocationVersion = "21.3.0"
    const val playServiceAuthVersion = "21.4.0"
    const val playServiceAdsVersion = "24.4.0"
    const val playServiceAdIdsVersion = "18.2.0"
    const val playServiceAnalyticsVersion = "18.1.1"
    const val firebaseBomVersion = "34.0.0"
    const val pusherVersion = "2.4.4"
    const val constraintVersion = "2.2.1"
    const val swipeRefreshLayoutVersion = "1.1.0"
    const val glideVersion = "4.16.0"
    const val daggerVersion = "2.52"
    const val playAppUpdate = "2.1.0"
    const val playIntegrityVersion = "1.4.0"
    const val pagingVersion = "2.1.2"
    const val workManagerVersion = "2.10.2"
    const val preferenceVersion = "1.2.0"
    const val navigationVersion = "2.7.7"

    const val reLinker = "1.4.5"
    const val httpclient = "4.5.8"
    const val simbioEncryption = "1.2.0"
    const val flexbox = "3.0.0"
    const val segmented = "1.0.6"
    const val nvi18n = "1.29"
    const val exoplayer = "2.19.1"
    const val billing = "8.0.0"
    const val retrofit = "3.0.0"
    const val cameraView = "2.7.2"
    const val circleimageview = "3.1.0"
    const val roundedimageview = "2.3.0"
    const val branchIO = "5.15.2"
    const val geniusBlur = "2.0.0"
    const val fbShimmer = "0.5.0"
    const val lottie = "6.6.7"
    const val roomVersion = "2.7.2"
    const val appodeal = "*******"
    const val pollfish = "6.5.0"
    const val pageindicatorview = "1.0.3"
    const val shortcutBadger = "1.1.22"
    const val spotlight = "2.0.5"
    const val imageCropper = "2.8.0"

    const val rxbindingVersion = "2.2.0"
    const val lifecycleReactivestreamsVersion = "2.7.0"

    const val appcompatVersion = "1.7.1"
    const val fragmentKtxVersion = "1.8.8"
    const val activityKtxVersion = "1.10.1"
    const val lifecycleVersion = "2.9.2"
    const val recyclerviewVersion = "1.4.0"
    const val materialVersion = "1.12.0"
    const val anotationVersion = "1.8.2"
    const val paletteVersion = "1.0.0"
    const val emojiAppcompatVersion = "1.1.0"
    const val emoji2Version = "1.5.0"
    const val playServicesIdentity = "18.1.0"
    const val gson = "2.13.1"
    const val okhttpVersion = "5.1.0"
    const val eventBusVersion = "3.3.1"
    const val rxjavaVersion = "2.2.21"
    const val rxAndroidVersion = "2.1.1"
    const val rxBinding = "3.1.0"
    const val autoDispose = "1.4.1"
    const val coroutine = "1.10.2"
    const val coroutineAutoDispose = "0.3.1"

    //NSFW detector
    const val mlkitImageLabelingVersion = "17.0.7"
    const val mlkitImageLabelingCustomVersion = "17.0.3"
    const val mlkitFaceDetectionVersion = "17.1.0"
    const val mlkitTextRecognitionVersion = "19.0.1"

    const val timber = "5.0.1"
    const val leakcanary = "2.10.0"
    const val chucker = "3.5.2"
    const val anrwatchdog = "1.4.0"
    const val toolargetool = "0.3.0" //adb logcat -s TooLargeTool
}

//object TestVersions {
//    //const val runner = "1.0.2"
//    const val rules = "1.0.2"
//    //const val espressoCore = "3.0.2"
//    //const val junitVersion = "4.12"
//    //const val assertjVersion = "3.14.0"
//    //const val assertjAndroidVersion = "1.2.0"
//    //const val mockitoVersion = "3.1.0"
//    //const val robolectricVersion = "4.3.1"
//}

object ToolVersions {
    const val googleService = "4.4.3"
    const val benManesGradleVersionsPlugin = "0.48.0"
    const val kotlinGradlePlugin = "2.2.0"
    const val crashlytics = "3.0.5"
}

object Libs {
    val supportLibs = arrayOf("androidx.dynamicanimation:dynamicanimation:$supportSpringAnimationVersion",
            "androidx.percentlayout:percentlayout:$supportPercentLayoutVersion",
            "androidx.preference:preference:$preferenceVersion")

    val navigationLibs = arrayOf("androidx.navigation:navigation-fragment-ktx:$navigationVersion",
                                 "androidx.navigation:navigation-ui-ktx:$navigationVersion")

    val firebaseLibs = arrayOf(
        "com.google.firebase:firebase-messaging",
        "com.google.firebase:firebase-analytics",
        "com.google.firebase:firebase-crashlytics"
    )

    val glideLib = arrayOf("com.github.bumptech.glide:okhttp3-integration:$glideVersion@aar")

    val annotationProcessorLibs = arrayOf("com.github.bumptech.glide:ksp:$glideVersion"
            , "com.google.dagger:dagger-compiler:$daggerVersion")

    val daggerLib = arrayOf("com.google.dagger:dagger:$daggerVersion")

    object Epoxy {
        private const val version = "5.1.4"
        const val core = "com.airbnb.android:epoxy:${version}"
        const val binding = "com.airbnb.android:epoxy-databinding:${version}"
        const val processor = "com.airbnb.android:epoxy-processor:${version}"
        const val preLoading = "com.airbnb.android:epoxy-glide-preloading:${version}"
    }
}