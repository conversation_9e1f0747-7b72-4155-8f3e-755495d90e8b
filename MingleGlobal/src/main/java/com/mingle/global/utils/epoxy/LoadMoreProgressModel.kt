package com.mingle.global.utils.epoxy

import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.mingle.global.R
import com.mingle.global.utils.epoxy.BaseEpoxyHolder

@EpoxyModelClass
abstract class LoadMoreProgressModel : EpoxyModelWithHolder<LoadMoreProgressModel.Holder>() {

    @EpoxyAttribute
    var inStaggeredLayout: Boolean = false

    override fun bind(holder: Holder) {
        if (inStaggeredLayout) {
            if (holder.view.layoutParams is StaggeredGridLayoutManager.LayoutParams) {
                val layoutParams = holder.view.layoutParams as? StaggeredGridLayoutManager.LayoutParams
                layoutParams?.isFullSpan = true
            }
        }
    }

    override fun getDefaultLayout(): Int {
        return R.layout.load_more_progress_layout
    }

    inner class Holder: BaseEpoxyHolder()
}