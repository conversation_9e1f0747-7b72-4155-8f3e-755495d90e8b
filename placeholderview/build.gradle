apply plugin: 'com.android.library'

android {
    namespace 'com.mindorks.placeholderview'
    compileSdk Versions.compileSdkVersion

    defaultConfig {
        minSdk = Versions.minSdkVersion
        targetSdk = Versions.targetSdkVersion
        consumerProguardFiles 'proguard-rules.pro'
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
}

dependencies {
    testImplementation 'junit:junit:4.13.2'
    implementation "androidx.appcompat:appcompat:${Versions.appcompatVersion}"
    implementation "androidx.recyclerview:recyclerview:${Versions.recyclerviewVersion}"
    implementation "androidx.gridlayout:gridlayout:1.0.0"
    api project(':placeholderview-annotations')
}

//apply from: 'publish.gradle'