//apply plugin: 'com.github.dcendents.android-maven'
//apply plugin: "com.jfrog.bintray"
//
//def siteUrl = 'https://github.com/janishar/PlaceHolderView'
//def gitUrl = 'https://github.com/janishar/PlaceHolderView.git'
////
////group = rootProject.ext.libPublishGroupName
////version = rootProject.ext.libPublishVersionName
////
////install {
////    repositories.mavenInstaller {
////        pom.project {
////            packaging 'aar'
////
////            name 'placeholderview'
////            description 'Views Built on RecyclerView and Swipe View'
////
////            url siteUrl
////
////            licenses {
////                license {
////                    name 'The Apache Software License, Version 2.0'
////                    url 'http://www.apache.org/licenses/LICENSE-2.0.txt'
////                }
////            }
////
////            developers {
////                developer {
////                    id '<EMAIL>'
////                    name 'Janishar Ali'
////                    email '<EMAIL>'
////                }
////            }
////
////            scm {
////                connection gitUrl
////                developerConnection gitUrl
////                url siteUrl
////            }
////        }
////    }
////}
////
////task sourcesJar(type: Jar) {
////    from android.sourceSets.main.java.srcDirs
////    classifier = 'sources'
////}
////
////task javadoc(type: Javadoc) {
////    source = android.sourceSets.main.java.srcDirs
////    classpath += project.files(android.getBootClasspath().join(File.pathSeparator))
////    classpath += configurations.compile
////}
////
////task javadocJar(type: Jar, dependsOn: javadoc) {
////    classifier = 'javadoc'
////    from javadoc.destinationDir
////}
////artifacts {
////    archives javadocJar
////    archives sourcesJar
////}
////
////if (project.rootProject.file("local.properties").exists()) {
////    Properties properties = new Properties()
////    properties.load(project.rootProject.file('local.properties').newDataInputStream())
////
////    bintray {
////        user = properties.getProperty("bintray.user")
////        key = properties.getProperty("bintray.apikey")
////
////        configurations = ['archives']
////        dryRun = false
////
////        pkg {
////            repo = "mindorks"
////            name = "placeholderview-2"
////            websiteUrl = siteUrl
////            vcsUrl = gitUrl
////            licenses = ["Apache-2.0"]
////            publish = true
////        }
////    }
////}