package com.mindorks.placeholderview;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by j<PERSON><PERSON><PERSON><PERSON> on 05/10/16.
 */

public class InfinitePlaceHolderView extends PlaceHolderView {

    private boolean mIsLoadingMore = false;
    private boolean mNoMoreToLoad = false;
    private BaseResolver mLoadMoreResolver;
    private LoadMoreCallbackBinder mLoadMoreCallbackBinder;
    private PlaceHolderView.OnScrollListener mOnScrollListener;

    public InfinitePlaceHolderView(Context context) {
        super(context);
    }

    public InfinitePlaceHolderView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public InfinitePlaceHolderView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    private void setLoadMoreListener() {
        mOnScrollListener =
                new PlaceHolderView.OnScrollListener() {
                    @SuppressWarnings("unchecked")
                    @Override
                    public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                        super.onScrolled(recyclerView, dx, dy);
                        LayoutManager layoutManager = recyclerView.getLayoutManager();
                        if(layoutManager instanceof LinearLayoutManager) {
                            LinearLayoutManager linearLayoutManager = (LinearLayoutManager) layoutManager;
                            int totalItemCount = linearLayoutManager.getItemCount();
                            int lastVisibleItem = linearLayoutManager.findLastVisibleItemPosition();
                            if (!mIsLoadingMore
                                    && !mNoMoreToLoad
                                    && totalItemCount > 0
                                    && totalItemCount == lastVisibleItem + 1) {
                                mIsLoadingMore = true;
                                new Handler(Looper.getMainLooper()).post(new Runnable() {
                                    @Override
                                    public void run() {
                                        addView(mLoadMoreResolver);
                                        mLoadMoreCallbackBinder.bindLoadMore(mLoadMoreResolver);
                                    }
                                });
                            }
                        }
                    }
                };
        addOnScrollListener(mOnScrollListener);
    }

    public <T extends BaseResolver>void setLoadMoreResolver(T loadMoreResolver) {
        mLoadMoreResolver = loadMoreResolver;
        mLoadMoreCallbackBinder = Binding.bindLoadMoreCallback(loadMoreResolver);
        mNoMoreToLoad = false;
        setLoadMoreListener();
    }

    public void noMoreToLoad(){
        mNoMoreToLoad = true;
        removeOnScrollListener(mOnScrollListener);
    }

    public void loadingDone(){
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                removeView(mLoadMoreResolver);
                mIsLoadingMore = false;
            }
        });
    }

    public int getViewCount() {
        return super.getViewResolverCount() - 1;
    }
}
