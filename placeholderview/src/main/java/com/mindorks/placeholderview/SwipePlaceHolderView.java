package com.mindorks.placeholderview;

import android.annotation.TargetApi;
import android.content.Context;
import android.os.Build;
import android.os.CountDownTimer;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.mindorks.placeholderview.listeners.ItemRemovedListener;
import com.mindorks.placeholderview.listeners.OnSwipeTouchListener;
import com.mindorks.placeholderview.widget.FrameLayoutWithAnimation;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by janisha<PERSON><PERSON> on 26/08/16.
 */
public class SwipePlaceHolderView extends FrameLayout implements
        SwipeViewBinder.SwipeCallback<SwipeViewBinder<BaseResolver,
                SwipePlaceHolderView.FrameView,
                SwipePlaceHolderView.SwipeOption,
                SwipeDecor>> {

    public static final int DEFAULT_DISPLAY_VIEW_COUNT = 20;
    public static final int SWIPE_TYPE_DEFAULT = 1;
    public static final int SWIPE_TYPE_HORIZONTAL = 2;
    public static final int SWIPE_TYPE_VERTICAL = 3;

    private SwipeOption mSwipeOption;
    private SwipeViewBuilder<SwipePlaceHolderView> mSwipeViewBuilder;
    private LayoutInflater mLayoutInflater;
    private int mDisplayViewCount = DEFAULT_DISPLAY_VIEW_COUNT;
    private int mSwipeType = SWIPE_TYPE_DEFAULT;
    private boolean mIsReverse = false;
    private SwipeDecor mSwipeDecor;
    private List<SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor>> mSwipeViewBinderList;

    // TODO: Make mIsBtnSwipeDone a AtomicBoolean, to make it thread safe.
    private boolean mIsBtnSwipeDone = true;

    private BaseResolver mRestoreResolverOnUndo;
    private int mRestoreResolverLastPosition;
    private Boolean shouldShowUndoLastSwipeHint = null;
    private final ArrayList<ItemRemovedListener> mItemRemovedListeners = new ArrayList<>();
    private float mPreviousSwipeAngle = 0f;

    public SwipePlaceHolderView(Context context) {
        super(context);
        setupView(new ArrayList<SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor>>(),
                new SwipeViewBuilder<>(this),
                new SwipeOption(),
                new SwipeDecor());
    }

    public SwipePlaceHolderView(Context context, AttributeSet attrs) {
        super(context, attrs);
        setupView(new ArrayList<SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor>>(),
                new SwipeViewBuilder<>(this),
                new SwipeOption(),
                new SwipeDecor());
    }

    public SwipePlaceHolderView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setupView(new ArrayList<SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor>>(),
                new SwipeViewBuilder<>(this),
                new SwipeOption(),
                new SwipeDecor());
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public SwipePlaceHolderView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        setupView(new ArrayList<SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor>>(),
                new SwipeViewBuilder<>(this),
                new SwipeOption(),
                new SwipeDecor());
    }

    @Override
    protected int getChildDrawingOrder(int childCount, int i) {
        if (mIsReverse) {
            return super.getChildDrawingOrder(childCount, childCount - 1 - i);
        } else {
            return super.getChildDrawingOrder(childCount, i);
        }
    }

    protected <S extends
            SwipeViewBinder<?, ? extends FrameView, ? extends SwipeOption, ? extends SwipeDecor>,
            P extends SwipeOption,
            Q extends SwipeDecor,
            T extends SwipeViewBuilder<?>>
    void setupView(List<S> swipeViewBinderList, T swipeViewBuilder, P swipeOption, Q swipeDecor) {

        mSwipeViewBinderList =
                (List<SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor>>) swipeViewBinderList;
        mSwipeViewBuilder = (SwipeViewBuilder<SwipePlaceHolderView>) swipeViewBuilder;
        mLayoutInflater =  LayoutInflater.from(getContext());
        mSwipeDecor = swipeDecor;
        mSwipeOption = swipeOption;
        setChildrenDrawingOrderEnabled(true);
    }

    public <T extends SwipePlaceHolderView, S extends SwipeViewBuilder<T>> S getBuilder() {
        return (S) mSwipeViewBuilder;
    }

    protected SwipeOption getSwipeOption() {
        return mSwipeOption;
    }

    protected SwipeViewBuilder<SwipePlaceHolderView> getSwipeViewBuilder() {
        return mSwipeViewBuilder;
    }

    protected LayoutInflater getLayoutInflater() {
        return mLayoutInflater;
    }

    protected int getDisplayViewCount() {
        return mDisplayViewCount;
    }

    protected void setDisplayViewCount(int displayViewCount) {
        mDisplayViewCount = displayViewCount;
    }

    protected int getSwipeType() {
        return mSwipeType;
    }

    protected void setSwipeType(int swipeType) {
        mSwipeType = swipeType;
    }

    protected boolean isIsReverse() {
        return mIsReverse;
    }

    protected void setIsReverse(boolean isReverse) {
        mIsReverse = isReverse;
    }

    public SwipeDecor getSwipeDecor() {
        return mSwipeDecor;
    }

    protected void setSwipeDecor(SwipeDecor swipeDecor) {
        if(swipeDecor != null) {
            mSwipeDecor = swipeDecor;
        }
    }

    protected List<SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor>> getSwipeViewBinderList() {
        return mSwipeViewBinderList;
    }

    protected boolean isIsBtnSwipeDone() {
        return mIsBtnSwipeDone;
    }

    protected boolean isUndoEnabled() {
        return mSwipeOption.isUndoEnabled();
    }

    protected void setIsUndoEnabled(boolean isUndoEnabled) {
        mSwipeOption.setIsUndoEnabled(isUndoEnabled);
    }

    public BaseResolver getRestoreResolverOnUndo() {
        return mRestoreResolverOnUndo;
    }

    public void setRestoreResolverOnUndo(BaseResolver restoreResolverOnUndo) {
        this.mRestoreResolverOnUndo = restoreResolverOnUndo;
    }

    public void clearRestoreResolverOnUndo() {
        this.mRestoreResolverOnUndo = null;
        if (!mItemRemovedListeners.isEmpty()) {
            for (ItemRemovedListener itemRemovedListener : mItemRemovedListeners) {
                itemRemovedListener.onItemRemoved(mSwipeViewBinderList.size());
            }
        }
    }

    protected int getRestoreResolverLastPosition() {
        return mRestoreResolverLastPosition;
    }

    public Boolean getShouldShowUndoLastSwipeHint() {
        return shouldShowUndoLastSwipeHint;
    }

    public void setShouldShowUndoLastSwipeHint(Boolean shouldShowUndoLastSwipeHint) {
        this.shouldShowUndoLastSwipeHint = shouldShowUndoLastSwipeHint;
    }

    protected void setWidthSwipeDistFactor(float factor) {
        mSwipeOption.setWidthSwipeDistFactor(factor);
    }

    protected void setHeightSwipeDistFactor(float factor) {
        mSwipeOption.setHeightSwipeDistFactor(factor);
    }

    protected ArrayList<ItemRemovedListener> getItemRemovedListeners() {
        return mItemRemovedListeners;
    }

    /*
     * This method make it possible to construct ViewBinder specific to the Swipe View
     */
    protected <T extends BaseResolver,
            F extends FrameView,
            P extends SwipeOption,
            Q extends SwipeDecor,
            V extends SwipeViewBinder<T, F, P, Q>>
    V getViewBinder(T resolver) {
        return (V) Binding.bindSwipeViewResolver(resolver);
    }

    public <T extends BaseResolver> SwipePlaceHolderView addView(T resolver) {
        SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor> swipeViewBinder = this.getViewBinder(resolver);
        mSwipeViewBinderList.add(swipeViewBinder);
        if (mSwipeViewBinderList.size() <= mDisplayViewCount) {
            int positionInFrameLayout = 0; // FrameLayout stacking BOTTOM to TOP (View at position 0 will display last)
            int swipeViewBinderPosition = mSwipeViewBinderList.indexOf(swipeViewBinder); // SwipePlaceHolderView display TOP to BOTTOM (View at position 0 will display first)
            FrameView frameView = new FrameView(getContext(), mSwipeOption.getIsSwipeViewLocked()
                    , mSwipeOption.getIsSwipeHorizontalLocked(), mSwipeOption.getIsSwipeVerticalLocked());
            frameView.setLayoutParams(getLayoutParamsWithSwipeDecor(swipeViewBinderPosition, mSwipeDecor));
            mLayoutInflater.inflate(swipeViewBinder.getLayoutId(), frameView, true);
            attachSwipeInfoViews(frameView, swipeViewBinder, mSwipeDecor);
            addView(frameView, positionInFrameLayout);
            setRelativeScale(frameView, swipeViewBinderPosition, mSwipeDecor);
            swipeViewBinder.bindView(frameView, swipeViewBinderPosition, mSwipeType, mSwipeDecor, mSwipeOption, this);

            if (swipeViewBinderPosition == 0) {
                swipeViewBinder.setOnTouch();
            }
        }
        return this;
    }

    protected <T extends BaseResolver> void addView(T resolver, int swipeViewBinderPosition) {
        SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor> swipeViewBinder = this.getViewBinder(resolver);
        mSwipeViewBinderList.add(swipeViewBinderPosition, swipeViewBinder);
        if (swipeViewBinderPosition < mDisplayViewCount) {
            FrameView frameView = new FrameView(getContext(), mSwipeOption.getIsSwipeViewLocked()
                    , mSwipeOption.getIsSwipeHorizontalLocked(), mSwipeOption.getIsSwipeVerticalLocked());
            frameView.setLayoutParams(getLayoutParamsWithSwipeDecor(swipeViewBinderPosition, mSwipeDecor));
            mLayoutInflater.inflate(swipeViewBinder.getLayoutId(), frameView, true);
            attachSwipeInfoViews(frameView, swipeViewBinder, mSwipeDecor);
            // FrameLayout stacking BOTTOM to TOP (View at position 0 will display last)
            int positionInFrameLayout = Math.max(getChildCount() - swipeViewBinderPosition, 0);
            addView(frameView, positionInFrameLayout);
            setRelativeScale(frameView, swipeViewBinderPosition, mSwipeDecor);
            swipeViewBinder.bindView(frameView, swipeViewBinderPosition, mSwipeType, mSwipeDecor, mSwipeOption, this);

            if (swipeViewBinderPosition == 0) {
                swipeViewBinder.setOnTouch();
            }
        }
    }

    protected <T> void addPendingView(SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor> swipeViewBinder) {
        int positionInFrameLayout = 0; // FrameLayout stacking BOTTOM to TOP (View at position 0 will display last)
        int swipeViewBinderPosition = mSwipeViewBinderList.indexOf(swipeViewBinder); // SwipePlaceHolderView display TOP to BOTTOM (View at position 0 will display first)
        FrameView frameView = new FrameView(getContext(), mSwipeOption.getIsSwipeViewLocked()
                , mSwipeOption.getIsSwipeHorizontalLocked(), mSwipeOption.getIsSwipeVerticalLocked());
        frameView.setLayoutParams(getLayoutParamsWithSwipeDecor(swipeViewBinderPosition, mSwipeDecor));
        mLayoutInflater.inflate(swipeViewBinder.getLayoutId(), frameView, true);
        attachSwipeInfoViews(frameView, swipeViewBinder, mSwipeDecor);
        addView(frameView, positionInFrameLayout);
        setRelativeScale(frameView, swipeViewBinderPosition, mSwipeDecor);
        swipeViewBinder.bindView(frameView, swipeViewBinderPosition, mSwipeType, mSwipeDecor, mSwipeOption, this);
    }

    protected <V extends FrameLayout, T extends SwipeViewBinder>void attachSwipeInfoViews(V frame, T swipeViewBinder, SwipeDecor swipeDecor){

        if(!swipeDecor.getSwipeLayoutIgnoreMsgViewList().contains(swipeViewBinder.getLayoutId())
                && swipeDecor.getSwipeInMsgLayoutId() != SwipeDecor.PRIMITIVE_NULL
                && swipeDecor.getSwipeOutMsgLayoutId() != SwipeDecor.PRIMITIVE_NULL){

            FrameLayout swipeInMsgView = new FrameLayoutWithAnimation(getContext());
            FrameLayout swipeOutMsgView = new FrameLayoutWithAnimation(getContext());

            FrameLayout.LayoutParams layoutParamsInMsg = new FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);

            FrameLayout.LayoutParams layoutParamsOutMsg = new FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);

            layoutParamsInMsg.gravity = mSwipeDecor.getSwipeInMsgGravity();
            layoutParamsOutMsg.gravity = mSwipeDecor.getSwipeOutMsgGravity();

            swipeInMsgView.setLayoutParams(layoutParamsInMsg);
            swipeOutMsgView.setLayoutParams(layoutParamsOutMsg);

            mLayoutInflater.inflate(swipeDecor.getSwipeInMsgLayoutId(), swipeInMsgView, true);
            mLayoutInflater.inflate(swipeDecor.getSwipeOutMsgLayoutId(), swipeOutMsgView, true);

            frame.addView(swipeInMsgView);
            frame.addView(swipeOutMsgView);

            swipeInMsgView.setVisibility(GONE);
            swipeOutMsgView.setVisibility(GONE);

            swipeViewBinder.setSwipeInMsgView(swipeInMsgView);
            swipeViewBinder.setSwipeOutMsgView(swipeOutMsgView);
        }
    }

    protected FrameLayout.LayoutParams getLayoutParamsWithSwipeDecor(int position, SwipeDecor decor){

        if(decor.getViewHeight() != 0 && decor.getViewWidth() != 0) {
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(decor.getViewWidth(), decor.getViewHeight());
            layoutParams.gravity = decor.getViewGravity();
            layoutParams.setMargins(decor.getMarginLeft() + decor.getPaddingLeft() * position,
                    decor.getMarginTop() + decor.getPaddingTop() * position, 0, 0);
            return layoutParams;
        }else{
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.gravity = decor.getViewGravity();
            layoutParams.setMargins(decor.getMarginLeft() + decor.getPaddingLeft() * position,
                    decor.getMarginTop() + decor.getPaddingTop() * position, 0, 0);
            return layoutParams;
        }
    }

    protected <V extends  FrameLayout>void setLayoutParamsWithSwipeDecor(V frame, int position, SwipeDecor decor){
        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams)frame.getLayoutParams();
        layoutParams.setMargins(decor.getMarginLeft() + decor.getPaddingLeft() * position,
                decor.getMarginTop() + decor.getPaddingTop() * position, 0, 0);
        frame.setLayoutParams(layoutParams);
    }

    protected <V extends View, T extends SwipeDecor>void setRelativeScale(V view, int position,  T swipeDecor){
        view.setScaleX( 1 - position * swipeDecor.getRelativeScale());
        view.setScaleY(1 - position * swipeDecor.getRelativeScale());
    }

    public void doSwipe(Object resolver, boolean isSwipeIn){
        if(mIsBtnSwipeDone){
            mIsBtnSwipeDone = false;
            SwipeViewBinder swipeViewBinder = null;
            for(SwipeViewBinder viewBinder : mSwipeViewBinderList){
                if(viewBinder.getResolver() == resolver){
                    swipeViewBinder = viewBinder;
                    break;
                }
            }

            if(swipeViewBinder != null){
                swipeViewBinder.doSwipe(isSwipeIn);
            }
            new CountDownTimer((int)(2.25 * mSwipeDecor.getSwipeAnimTime()), mSwipeDecor.getSwipeAnimTime()) {
                public void onTick(long millisUntilFinished) {
                }
                public void onFinish() {
                    mIsBtnSwipeDone = true;
                }
            }.start();
        }
    }

    public void cancelCurrentTouch() {
        if (mSwipeViewBinderList != null && mSwipeViewBinderList.size() > 0 && mSwipeViewBinderList.get(0) != null
            && mSwipeViewBinderList.get(0).getLayoutView() != null) {
            mSwipeViewBinderList.get(0).getLayoutView().dispatchTouchEvent(MotionEvent.obtain(0, 0, MotionEvent.ACTION_CANCEL, 0, 0, 0));
        }
    }

    public void doSwipe(boolean isSwipeIn){
        if(mIsBtnSwipeDone) {
            mIsBtnSwipeDone = false;
            if (mSwipeViewBinderList.size() > 0) {
                mSwipeViewBinderList.get(0).doSwipe(isSwipeIn);
            }

            new CountDownTimer((mSwipeDecor.getSwipeAnimTime()), mSwipeDecor.getSwipeAnimTime()) {
                public void onTick(long millisUntilFinished) {
                }
                public void onFinish() {
                    mIsBtnSwipeDone = true;
                }
            }.start();
        }
    }

    public void lockViews(){
        mSwipeOption.setIsViewLocked(true);
    }

    public void unlockViews(){
        mSwipeOption.setIsViewLocked(false);
    }

    public void lockSwipeHorizontalViews() {
        mSwipeOption.setIsSwipeHorizontalLocked(true);
    }

    public void unlockSwipeHorizontalViews() {
        mSwipeOption.setIsSwipeHorizontalLocked(false);
    }

    public void lockSwipeVerticalViews() {
        mSwipeOption.setIsSwipeVerticalLocked(true);
    }

    public void unlockSwipeVerticalViews() {
        mSwipeOption.setIsSwipeVerticalLocked(false);
    }

    public void activatePutBack(){
        mSwipeOption.setIsPutBackActive(true);
    }

    public void deactivatePutBack(){
        mSwipeOption.setIsPutBackActive(false);
    }

    public void disableTouchSwipe() {
        mSwipeOption.setIsTouchSwipeLocked(true);
    }

    public void enableTouchSwipe() {
        mSwipeOption.setIsTouchSwipeLocked(false);
    }

    public void disableSwipeView() {
        mSwipeOption.setIsTouchSwipeLocked(true);
        mSwipeOption.setIsSwipeViewLocked(true);
    }

    public void enableSwipeView() {
        mSwipeOption.setIsTouchSwipeLocked(false);
        mSwipeOption.setIsSwipeViewLocked(false);
    }

    @Override
    public void onRemoveView(SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor> swipeViewBinder) {
        SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor> newSwipeViewBinder = null;
        int newSwipeViewBinderPosition = SwipeDecor.PRIMITIVE_NULL;

        if (mSwipeViewBinderList.size() > mDisplayViewCount) {
            newSwipeViewBinder = mSwipeViewBinderList.get(mDisplayViewCount);
            newSwipeViewBinderPosition = mSwipeViewBinderList.indexOf(newSwipeViewBinder);
        }

        mSwipeViewBinderList.remove(swipeViewBinder);
        removeView(swipeViewBinder.getLayoutView());
        if (newSwipeViewBinder != null && newSwipeViewBinderPosition != SwipeDecor.PRIMITIVE_NULL) {
            addPendingView(newSwipeViewBinder);
            newSwipeViewBinderPosition -= 1;
        } else {
            newSwipeViewBinderPosition = mSwipeViewBinderList.size() - 1;
        }
        resetViewOrientation(newSwipeViewBinderPosition, mSwipeDecor);

        if (!mSwipeViewBinderList.isEmpty()) {
            mSwipeViewBinderList.get(0).setOnTouch();
        }
        if (mSwipeOption.isUndoEnabled()
                && swipeViewBinder.getResolver() != null && swipeViewBinder.getResolver().isEnabledForUndo()) {
            mRestoreResolverOnUndo = swipeViewBinder.getResolver();
            mRestoreResolverLastPosition = newSwipeViewBinderPosition;
        }
        swipeViewBinder.unbind();
        if (!mItemRemovedListeners.isEmpty()) {
            for (ItemRemovedListener itemRemovedListener : mItemRemovedListeners) {
                itemRemovedListener.onItemRemoved(mSwipeViewBinderList.size());
            }
        }
    }

    @Override
    public void onAnimateView(float distXMoved,
                              float distYMoved,
                              float finalXDist,
                              float finalYDist,
                              SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor> swipeViewBinder,
                              float startX, float currentX, int halfScreenWidth) {

        float distXMovedAbs = distXMoved > 0 ? distXMoved : -distXMoved;
        float distYMovedAbs = distYMoved > 0 ? distYMoved : -distYMoved;

        if(mSwipeDecor.isAnimateScale() && mSwipeViewBinderList.contains(swipeViewBinder)
                && distXMovedAbs <= finalXDist && distYMovedAbs <= finalYDist){
            int count;
            float distMoved;
            float finalDist;
            if(distXMovedAbs > distYMovedAbs){
                distMoved = distXMovedAbs;
                finalDist = finalXDist;
            }else{
                distMoved = distYMovedAbs;
                finalDist = finalYDist;
            }

            if(mSwipeViewBinderList.size() > mDisplayViewCount){
                count = mDisplayViewCount;
            }else{
                count  = mSwipeViewBinderList.size();
            }

            for(int i = mSwipeViewBinderList.indexOf(swipeViewBinder) +  1; i < count; i++){

                SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor>
                        swipeViewBinderBelow = mSwipeViewBinderList.get(i);
                float scaleDefault = 1 - i * mSwipeDecor.getRelativeScale();
                float scaleOfAboveViewDefault = 1 - (i - 1) * mSwipeDecor.getRelativeScale();
                float scale = ((scaleOfAboveViewDefault - scaleDefault) / finalDist) * distMoved + scaleDefault;
                swipeViewBinderBelow.getLayoutView().setScaleX(scale);
                swipeViewBinderBelow.getLayoutView().setScaleY(scale);

                FrameLayout.LayoutParams layoutParams =
                        (FrameLayout.LayoutParams) swipeViewBinderBelow.getLayoutView().getLayoutParams();
                float value = (-mSwipeDecor.getPaddingTop() / finalDist) * distMoved
                        + (mSwipeDecor.getMarginTop()+ mSwipeDecor.getPaddingTop() * i);
                layoutParams.topMargin = (int) value;

                value = (-mSwipeDecor.getPaddingLeft() / finalDist) * distMoved
                        + (mSwipeDecor.getMarginLeft() + mSwipeDecor.getPaddingLeft() * i);
                layoutParams.leftMargin = (int) value;

                swipeViewBinderBelow.getLayoutView().setLayoutParams(layoutParams);
            }
        }

        // detect rotation
        float angleMax = 0;
        angleMax = mSwipeDecor.getSwipeRotationAngle();
        float deltaDistance = currentX - startX;
        float angle = angleMax * (deltaDistance / halfScreenWidth);

//            float deltaAngleSign = angle - mPreviousSwipeAngle > 0 ? 1 : -1;
//            float deltaAngle = Math.abs(angle - mPreviousSwipeAngle);
//            deltaAngle = Math.min(mSwipeDecor.getSwipeMaxChangeAngle(), deltaAngle);
//            float finalAngle = mPreviousSwipeAngle + deltaAngleSign * deltaAngle;
//            mPreviousSwipeAngle = finalAngle;
        swipeViewBinder.getLayoutView().setRotation(angle);

        if((distXMovedAbs > mSwipeDecor.getSwipeDistToDisplayMsg()
                || distYMovedAbs > mSwipeDecor.getSwipeDistToDisplayMsg())){

            boolean isSwipeIn = false;
            if (distXMoved > 0) {
                isSwipeIn = true;
            } else if (distXMoved < 0) {
                isSwipeIn = false;
            } else {
                if (distYMoved > 0) {
                    isSwipeIn = true;
                } else if (distYMoved < 0) {
                    isSwipeIn = false;
                }
            }

            if (isSwipeIn) {
                swipeViewBinder.bindSwipeInState();
            } else {
                swipeViewBinder.bindSwipeOutState();
            }

            if (mSwipeDecor.getSwipeInMsgLayoutId() != SwipeDecor.PRIMITIVE_NULL
                    && mSwipeDecor.getSwipeOutMsgLayoutId() != SwipeDecor.PRIMITIVE_NULL) {

                if (isSwipeIn) {
                    if (swipeViewBinder.getSwipeInMsgView() instanceof FrameLayoutWithAnimation) {
                        ((FrameLayoutWithAnimation) swipeViewBinder.getSwipeInMsgView()).setVisibilityWithAnimation(View.VISIBLE);
                    }
                    if (swipeViewBinder.getSwipeOutMsgView() instanceof FrameLayoutWithAnimation) {
                        ((FrameLayoutWithAnimation) swipeViewBinder.getSwipeOutMsgView()).setVisibilityWithAnimation(View.GONE);
                    }
                } else {
                    if (swipeViewBinder.getSwipeInMsgView() instanceof FrameLayoutWithAnimation) {
                        ((FrameLayoutWithAnimation) swipeViewBinder.getSwipeInMsgView()).setVisibilityWithAnimation(View.GONE);
                    }
                    if (swipeViewBinder.getSwipeOutMsgView() instanceof FrameLayoutWithAnimation) {
                        ((FrameLayoutWithAnimation) swipeViewBinder.getSwipeOutMsgView()).setVisibilityWithAnimation(View.VISIBLE);
                    }
                }
            }
        }

        // stores the current margins for any operations later:
        // Example: For undo animation
        FrameLayout.LayoutParams layoutParams =
                (FrameLayout.LayoutParams) swipeViewBinder.getLayoutView().getLayoutParams();
        swipeViewBinder.setFinalLeftMargin(layoutParams.leftMargin);
        swipeViewBinder.setFinalTopMargin(layoutParams.topMargin);
    }

    @Override
    public void onResetView(SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor> swipeViewBinder) {
        if(mSwipeViewBinderList.size() > mDisplayViewCount){
            resetViewOrientation(mDisplayViewCount - 1, mSwipeDecor);
        }else{
            resetViewOrientation(mSwipeViewBinderList.size() - 1, mSwipeDecor);
        }

        if(mSwipeDecor.getSwipeInMsgLayoutId() != SwipeDecor.PRIMITIVE_NULL
                && mSwipeDecor.getSwipeOutMsgLayoutId() != SwipeDecor.PRIMITIVE_NULL) {
            if (swipeViewBinder.getSwipeInMsgView() instanceof FrameLayoutWithAnimation) {
                ((FrameLayoutWithAnimation) swipeViewBinder.getSwipeInMsgView()).setVisibilityWithAnimation(View.GONE);
            }

            if (swipeViewBinder.getSwipeOutMsgView() instanceof FrameLayoutWithAnimation) {
                ((FrameLayoutWithAnimation) swipeViewBinder.getSwipeOutMsgView()).setVisibilityWithAnimation(View.GONE);
            }
        }

        if (swipeViewBinder != null) {
            if (swipeViewBinder.getLayoutView() != null) {
                swipeViewBinder.getLayoutView().setRotation(0);
            }
            // check null get resolver is null
            // getResolver().onBindSwipeCancelState()
            if (swipeViewBinder.getResolver() != null) {
                swipeViewBinder.bindSwipeCancelState();
            }
            if (swipeViewBinder.getLayoutView() != null) {
                swipeViewBinder.getLayoutView().reset();
            }
        }
    }

    protected <T extends SwipeDecor>void resetViewOrientation(int lastPosition, T swipeDecor){
        if(swipeDecor.isAnimateScale() && lastPosition >= 0) {
            for (int i = 0; i <= lastPosition; i++) {
                if (mSwipeViewBinderList.get(i) != null) {
                    SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor>
                            swipeViewBinder = mSwipeViewBinderList.get(i);
                    setRelativeScale(swipeViewBinder.getLayoutView(), i, swipeDecor);
                    setLayoutParamsWithSwipeDecor(swipeViewBinder.getLayoutView(), i, swipeDecor);
                }
            }
        }
        mPreviousSwipeAngle = 0f;
    }

    public boolean canUndoLastSwipe() {
        return this.mRestoreResolverOnUndo != null;
    }

    public void undoLastSwipe(){
        if (mSwipeOption.isUndoEnabled() && mRestoreResolverOnUndo != null) {
            if (mRestoreResolverLastPosition >= 0 && mRestoreResolverLastPosition >= mDisplayViewCount - 1) {
                int positionInFrameLayout = Math.max(getChildCount() - 1 - mRestoreResolverLastPosition, 0);
                removeViewAt(positionInFrameLayout);
            }
            mRestoreResolverOnUndo.setEnabledForUndo(false);
            addView(mRestoreResolverOnUndo, 0);
            mRestoreResolverOnUndo = null;
            if (mSwipeViewBinderList.size() > 1) {
                SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor> lowerCard = mSwipeViewBinderList.get(1);
                //for condition where only one card is in the display
                if (lowerCard.getLayoutView() != null) {
                    lowerCard.blockTouch();
                }
            }

            SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor> topCard = mSwipeViewBinderList.get(0);
            topCard.doUndoAnimation();

            if (mRestoreResolverLastPosition >= 0 && mRestoreResolverLastPosition >= mDisplayViewCount - 1) {
                resetViewOrientation(mRestoreResolverLastPosition, mSwipeDecor);
            } else {
                resetViewOrientation(mRestoreResolverLastPosition + 1, mSwipeDecor);
            }
        }
    }

    public void addItemRemoveListener(ItemRemovedListener listener) {
        mItemRemovedListeners.add(listener);
    }

    public void removeItemRemoveListener(ItemRemovedListener listener) {
        mItemRemovedListeners.remove(listener);
    }

    public void setOnSwipeTouchListener(OnSwipeTouchListener listener) {
        mSwipeOption.setOnSwipeTouchListener(listener);
    }

    /**
     * @return the list containing the view objects added into the swipe view
     */
    public List<Object> getAllResolvers() {
        List<Object> resolverList = new ArrayList<>();
        for (SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor> binder : mSwipeViewBinderList) {
            resolverList.add(binder.getResolver());
        }
        return resolverList;
    }

    @Override
    public void removeAllViews() {
        Iterator<SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor>>
                iterator = mSwipeViewBinderList.iterator();
        while (iterator.hasNext()){
            SwipeViewBinder<BaseResolver, FrameView, SwipeOption, SwipeDecor>
                    swipeViewBinder = iterator.next();
            if(swipeViewBinder != null){
                swipeViewBinder.unbind();
            }
            iterator.remove();
        }
        mRestoreResolverOnUndo = null;
        mPreviousSwipeAngle = 0f;
        super.removeAllViews();
    }

    /**
     * Frame layout custom view to control the touch event propagation
     */
    public static class FrameView extends FrameLayout {

        private final int mTouchSlop;
        private boolean mIsBeingDragged = false;
        private int mLastMotionY;
        private int mLastMotionX;
        private final boolean mIsDisableTouch;
        private final boolean mIsSwipeHorizontalLocked;
        private final boolean mIsSwipeVerticalLocked;

        public FrameView(Context context, boolean isDisableTouch, boolean isSwipeHorizontalLocked, boolean isSwipeVerticalLocked) {
            super(context);
            ViewConfiguration vc = ViewConfiguration.get(getContext());
            mTouchSlop = vc.getScaledTouchSlop() + 8;
            mIsDisableTouch = isDisableTouch;
            mIsSwipeHorizontalLocked = isSwipeHorizontalLocked;
            mIsSwipeVerticalLocked = isSwipeVerticalLocked;
        }

        @Override
        public boolean onInterceptTouchEvent(MotionEvent ev) {
            if (mIsDisableTouch) return false;
            final int action = ev.getAction();
            if ((action == MotionEvent.ACTION_MOVE) && (mIsBeingDragged)) {
                return true;
            }

            switch (action & MotionEvent.ACTION_MASK) {
                case MotionEvent.ACTION_MOVE: {
                    final int y = (int) ev.getRawY();
                    final int x = (int) ev.getRawX();
                    if (!mIsSwipeVerticalLocked) {
                        final int yDiff = Math.abs(y - mLastMotionY);
                        if (yDiff > mTouchSlop) {
                            mIsBeingDragged = true;
                            mLastMotionY = y;
                        }
                    }
                    if (!mIsSwipeHorizontalLocked) {
                        final int xDiff = Math.abs(x - mLastMotionX);
                        if (xDiff > mTouchSlop) {
                            mIsBeingDragged = true;
                            mLastMotionX = x;
                        }
                    }
                    break;
                }

                case MotionEvent.ACTION_DOWN: {
                    mLastMotionY = (int) ev.getRawY();
                    mLastMotionX = (int) ev.getRawX();
                    break;
                }

                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    mIsBeingDragged = false;
                    break;
            }

            return mIsBeingDragged;
        }

        public void reset() {
            mIsBeingDragged = false;
        }
    }

    public static class SwipeOption {
        private float mWidthSwipeDistFactor = 3f;
        private float mHeightSwipeDistFactor = 3f;
        private final AtomicBoolean mIsViewLocked;
        private final AtomicBoolean mIsPutBackActive;
        private final AtomicBoolean mIsViewToRestoreOnLock;
        private final AtomicBoolean mIsViewToRestoreOnTouchLock;
        private final AtomicBoolean mIsTouchSwipeLocked;
        private final AtomicBoolean mIsSwipeViewLocked;
        private final AtomicBoolean mIsSwipeHorizontalLocked;
        private final AtomicBoolean mIsSwipeVerticalLocked;
        private boolean mIsUndoEnabled = false;
        private OnSwipeTouchListener mOnSwipeTouchListener;

        public SwipeOption() {
            mIsViewLocked = new AtomicBoolean(false);
            mIsPutBackActive = new AtomicBoolean(false);
            mIsViewToRestoreOnLock = new AtomicBoolean(true);
            mIsViewToRestoreOnTouchLock = new AtomicBoolean(true);
            mIsTouchSwipeLocked = new AtomicBoolean(false);
            mIsSwipeViewLocked = new AtomicBoolean(false);
            mIsSwipeHorizontalLocked = new AtomicBoolean(false);
            mIsSwipeVerticalLocked = new AtomicBoolean(false);
        }

        protected boolean getIsViewLocked() {
            return mIsViewLocked.get();
        }

        protected void setIsViewLocked(boolean isViewLocked) {
            this.mIsViewToRestoreOnLock.set(true);
            this.mIsViewLocked.set(isViewLocked);
        }

        protected boolean getIsPutBackActive() {
            return mIsPutBackActive.get();
        }

        protected void setIsPutBackActive(boolean isPutBackActive) {
            this.mIsPutBackActive.set(isPutBackActive);
        }

        protected boolean getIsViewToRestoredOnLock() {
            return mIsViewToRestoreOnLock.get();
        }

        protected void setIsViewToRestoredOnLock(boolean isViewToRestoredOnLock) {
            this.mIsViewToRestoreOnLock.set(isViewToRestoredOnLock);
        }

        protected boolean getIsViewToRestoreOnTouchLock() {
            return mIsViewToRestoreOnTouchLock.get();
        }

        protected void setIsViewToRestoreOnTouchLock(boolean isViewToRestoreOnTouchLock) {
            this.mIsViewToRestoreOnTouchLock.set(isViewToRestoreOnTouchLock);
        }

        protected boolean getIsTouchSwipeLocked(){
            return mIsTouchSwipeLocked.get();
        }

        protected void setIsTouchSwipeLocked(boolean locked){
            this.mIsViewToRestoreOnTouchLock.set(true);
            mIsTouchSwipeLocked.set(locked);
        }

        public float getWidthSwipeDistFactor() {
            return mWidthSwipeDistFactor;
        }

        public void setWidthSwipeDistFactor(float widthSwipeDistFactor) {
            this.mWidthSwipeDistFactor = widthSwipeDistFactor;
        }

        public float getHeightSwipeDistFactor() {
            return mHeightSwipeDistFactor;
        }

        public void setHeightSwipeDistFactor(float heightSwipeDistFactor) {
            this.mHeightSwipeDistFactor = heightSwipeDistFactor;
        }

        public boolean isUndoEnabled() {
            return mIsUndoEnabled;
        }

        public void setIsUndoEnabled(boolean isUndoEnabled) {
            mIsUndoEnabled = isUndoEnabled;
        }

        public boolean getIsSwipeViewLocked() {
            return mIsSwipeViewLocked.get();
        }

        public void setIsSwipeViewLocked(boolean isSwipeViewLocked) {
            this.mIsSwipeViewLocked.set(isSwipeViewLocked);
        }

        public boolean getIsSwipeHorizontalLocked() {
            return mIsSwipeHorizontalLocked.get();
        }

        public void setIsSwipeHorizontalLocked(boolean isLocked) {
            this.mIsSwipeHorizontalLocked.set(isLocked);
        }

        public boolean getIsSwipeVerticalLocked() {
            return mIsSwipeVerticalLocked.get();
        }

        public void setIsSwipeVerticalLocked(boolean isLocked) {
            this.mIsSwipeVerticalLocked.set(isLocked);
        }

        public void setOnSwipeTouchListener(OnSwipeTouchListener mOnSwipeTouchListener) {
            this.mOnSwipeTouchListener = mOnSwipeTouchListener;
        }

        public OnSwipeTouchListener getOnSwipeTouchListener() {
            return mOnSwipeTouchListener;
        }
    }
}
