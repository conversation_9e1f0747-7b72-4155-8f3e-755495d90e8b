package com.mindorks.placeholderview;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Created by jani<PERSON><PERSON><PERSON> on 27/03/16.
 */
public class SmoothLinearLayoutManager extends LinearLayoutManager {

    private final Context mContext;
    private int mPreloadSize = 0;

    public SmoothLinearLayoutManager(Context context){
        super(context, LinearLayoutManager.VERTICAL, false);
        mContext = context;
    }

    public SmoothLinearLayoutManager(Context context, int orientation, boolean reverseLayout) {
        super(context, orientation, reverseLayout);
        mContext = context;
        mPreloadSize = 4 * mContext.getResources().getDisplayMetrics().heightPixels;
    }

    public SmoothLinearLayoutManager(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        mContext = context;
        mPreloadSize = 4 * mContext.getResources().getDisplayMetrics().heightPixels;
    }

    @SuppressWarnings("deprecation")
    @Override
    protected int getExtraLayoutSpace(RecyclerView.State state) {
        return mPreloadSize;
    }

    @Override
    protected void calculateExtraLayoutSpace(@NonNull RecyclerView.State state, @NonNull int[] extraLayoutSpace) {
        super.calculateExtraLayoutSpace(state, extraLayoutSpace);
    }

    public void setPreloadSize(int mPreloadSize) {
        this.mPreloadSize = mPreloadSize;
    }
}
