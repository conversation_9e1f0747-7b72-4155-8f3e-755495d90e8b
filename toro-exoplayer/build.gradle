/*
 * Copyright (c) 2017 <PERSON>, <EMAIL>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

apply plugin: 'com.android.library'

android {
  compileSdk Versions.compileSdkVersion

  defaultConfig {
    namespace 'im.ene.toro.exoplayer'
    minSdkVersion Versions.minSdkVersion
    targetSdkVersion Versions.targetSdkVersion

    buildConfigField("String", "LIB_NAME", "\"" + "Toro ExoPlayer Extension, v" + Versions.toroVersion + "\"")
  }

  buildTypes {
    release {
      minifyEnabled false
      proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
    }
  }
    buildFeatures {
        buildConfig true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
}

dependencies {
  implementation project(':toro-core')
  implementation "androidx.appcompat:appcompat:${Versions.appcompatVersion}"
  implementation ("com.google.android.exoplayer:exoplayer:${Versions.exoplayer}") {
    exclude group: 'com.android.support'
    exclude group: 'com.google.android.gms'
  }

  implementation("com.google.android.exoplayer:extension-ima:${Versions.exoplayer}") {
    exclude group: 'com.android.support'
    exclude group: 'com.google.android.gms'
    exclude group: 'com.github.bumptech.glide'
  }

  implementation "androidx.recyclerview:recyclerview:${Versions.recyclerviewVersion}"
  implementation "androidx.annotation:annotation:${Versions.anotationVersion}"
}

ext {
  releaseArtifact = 'toro-ext-exoplayer'
  releaseDescription = 'The Toro Extension for ExoPlayer v2.'
}

project.archivesBaseName = "toro-ext-exoplayer"