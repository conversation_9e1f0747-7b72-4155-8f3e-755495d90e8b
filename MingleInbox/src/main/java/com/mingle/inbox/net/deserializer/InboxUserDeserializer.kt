package com.mingle.inbox.net.deserializer

import com.google.gson.Gson
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonParseException
import com.mingle.inbox.model.InboxUser
import java.lang.reflect.Type

class InboxUserDeserializer : JsonDeserializer<InboxUser?> {
    @Throws(JsonParseException::class)
    override fun deserialize(json: JsonElement, typeOfT: Type, context: JsonDeserializationContext): InboxUser? {
        val gson = Gson()
        val inboxUser = gson.fromJson(json, InboxUser::class.java)
        if (inboxUser != null && inboxUser.id != 0 && inboxUser.inboxUserId == 0) {
            inboxUser.inboxUserId = inboxUser.id
            inboxUser.id = 0
        }
        return inboxUser
    }
}
