package com.mingle.inbox.net;

import android.content.Context;
import android.text.TextUtils;

import com.chuckerteam.chucker.api.ChuckerCollector;
import com.chuckerteam.chucker.api.ChuckerInterceptor;
import com.chuckerteam.chucker.api.RetentionManager;
import com.google.android.play.core.integrity.IntegrityManagerFactory;
import com.google.android.play.core.integrity.StandardIntegrityManager;
import com.google.gson.FieldNamingPolicy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.mingle.global.interceptor.ActionTokenInterceptor;
import com.mingle.global.interceptor.PlayIntegrityTokenExecutor;
import com.mingle.global.net.MaintenanceInterceptor;
import com.mingle.global.net.deserializer.CustomDateTypeAdapter;
import com.mingle.inbox.BuildConfig;
import com.mingle.inbox.model.InboxConversation;
import com.mingle.inbox.model.InboxUser;
import com.mingle.inbox.model.credentials.InboxCredentials;
import com.mingle.inbox.model.request.UpdateConversationStatus;
import com.mingle.inbox.model.request.message.BroadcastMessage;
import com.mingle.inbox.model.request.message.ReportMessage;
import com.mingle.inbox.model.request.message.SendMessageAudio;
import com.mingle.inbox.model.request.message.SendMessageGiphy;
import com.mingle.inbox.model.request.message.SendMessagePhoto;
import com.mingle.inbox.model.request.message.SendMessageText;
import com.mingle.inbox.model.request.message.SendMessageVideo;
import com.mingle.inbox.model.response.CanSendMessageResponse;
import com.mingle.inbox.model.response.ConversationSettingsResponse;
import com.mingle.inbox.model.response.GetMessagesResponse;
import com.mingle.inbox.model.response.GetUnreadConversationCountResponse;
import com.mingle.inbox.model.response.RemainingDirectConversationsCanCreateResponse;
import com.mingle.inbox.model.response.SendMessageResponse;
import com.mingle.inbox.net.deserializer.InboxUserDeserializer;

import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.Completable;
import io.reactivex.Flowable;
import okhttp3.Credentials;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.PartMap;
import retrofit2.http.Path;
import retrofit2.http.QueryMap;

public class RetrofitConnector {
    private InboxApi inboxApi;
    private InboxS3Api inboxS3Api;
    private ActionTokenInterceptor actionTokenInterceptor;

    public RetrofitConnector(Context context, InboxCredentials inboxCredentials, String authToken, long cloudProjectNumber) {
        initialize(context, inboxCredentials, authToken, cloudProjectNumber);
    }

    public InboxApi getInboxApi() {
        return inboxApi;
    }

    public InboxS3Api getInboxS3Api() {
        return inboxS3Api;
    }

    public ActionTokenInterceptor getActionTokenInterceptor() {
        return actionTokenInterceptor;
    }

    protected Gson createGsonObject() {
        return new GsonBuilder()
                .serializeNulls()
                .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
                .registerTypeAdapter(Date.class, new CustomDateTypeAdapter())
                .registerTypeAdapter(InboxUser.class, new InboxUserDeserializer())
                .create();
    }

    private void initialize(Context context, InboxCredentials inboxCredentials, String authToken, long cloudProjectNumber) {
        StandardIntegrityManager integrityManager = IntegrityManagerFactory.createStandard(context);
        PlayIntegrityTokenExecutor integrityTokenExecutor = new PlayIntegrityTokenExecutor(integrityManager, cloudProjectNumber);
        actionTokenInterceptor = new ActionTokenInterceptor(
                context, inboxCredentials.inboxServerAddress, inboxCredentials.inboxServerSalt, integrityTokenExecutor);

        Interceptor inboxHeaderInterceptor = chain -> {
            Request request = chain.request();
            Request.Builder builder = request.newBuilder();
            if (!TextUtils.isEmpty(authToken)) {
                builder.addHeader("Auth-Token", authToken);
            }

            if (inboxCredentials.isStaging) {
                builder.addHeader("Authorization", Credentials.basic(
                        inboxCredentials.stagingAuthorizationUser, inboxCredentials.stagingAuthorizationPassword));
            } else {
                String authorizationToken = String.format("Token token=%s", inboxCredentials.inboxServerToken);
                builder.addHeader("Authorization", authorizationToken);
            }

            request = builder.build();
            return chain.proceed(request);
        };

        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        httpLoggingInterceptor.setLevel(BuildConfig.DEBUG ? HttpLoggingInterceptor.Level.BODY : HttpLoggingInterceptor.Level.NONE);

        ChuckerInterceptor chuckerInterceptor = new ChuckerInterceptor.Builder(context)
                .collector(new ChuckerCollector(context, true, RetentionManager.Period.ONE_HOUR))
                .maxContentLength(250_000L).alwaysReadResponseBody(true).build();

        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        builder.connectTimeout(60, TimeUnit.SECONDS);
        builder.readTimeout(60, TimeUnit.SECONDS);
        builder.addInterceptor(inboxHeaderInterceptor);
        builder.addInterceptor(actionTokenInterceptor);
        builder.addInterceptor(new MaintenanceInterceptor());
        builder.addInterceptor(httpLoggingInterceptor);
        builder.addInterceptor(chuckerInterceptor);

        OkHttpClient client = builder.build();
        Retrofit retrofit = new Retrofit.Builder()
                .client(client)
                .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
                .addConverterFactory(GsonConverterFactory.create(createGsonObject()))
                .baseUrl(inboxCredentials.inboxServerAddress)
                .build();
        inboxApi = retrofit.create(InboxApi.class);

        //S3
        HttpLoggingInterceptor s3LoggingInterceptor = new HttpLoggingInterceptor();
        s3LoggingInterceptor.setLevel(BuildConfig.DEBUG ? HttpLoggingInterceptor.Level.BASIC : HttpLoggingInterceptor.Level.NONE);
        OkHttpClient.Builder s3Builder = new OkHttpClient.Builder();
        s3Builder.readTimeout(60, TimeUnit.SECONDS);
        s3Builder.connectTimeout(60, TimeUnit.SECONDS);
        s3Builder.writeTimeout(6, TimeUnit.MINUTES);
        s3Builder.addInterceptor(s3LoggingInterceptor);
        s3Builder.addInterceptor(chuckerInterceptor);
        OkHttpClient s3Client = s3Builder.build();
        Retrofit s3Retrofit = new Retrofit.Builder()
                .client(s3Client)
                .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
                .baseUrl(inboxCredentials.inboxServerAddress).build();
        inboxS3Api = s3Retrofit.create(InboxS3Api.class);
    }

    public interface InboxApi {
        //<editor-fold desc="Conversation">
        @Headers({"Content-type: application/json"})
        @GET("/v3/new_conversations")
        Flowable<ArrayList<InboxConversation>> getNewConversations(@QueryMap Map<String, Object> params);

        @Headers({"Content-type: application/json"})
        @GET("/v3/conversations")
        Flowable<ArrayList<InboxConversation>> getOlderConversations(@QueryMap Map<String, Object> options);

        @Headers({"Content-type: application/json"})
        @GET("/v3/conversations/{conversation_id}")
        Flowable<InboxConversation> getConversation(@Path("conversation_id") int conversationId, @QueryMap Map<String, Object> params);

        @Headers({"Content-type: application/json"})
        @GET("/v3/public_users/{target_inbox_user_id}/conversation")
        Flowable<InboxConversation> getConversationByInboxUserId(@Path("target_inbox_user_id") int targetInboxUserId, @QueryMap Map<String, Object> params);

        @Headers({"Content-type: application/json"})
        @PUT("/v3/conversations/{conversation_id}/mark_as_read")
        Completable updateConversationStatus(@Body UpdateConversationStatus request, @Path("conversation_id") int conversationId,
                                             @QueryMap Map<String, Object> params);

        @Headers({"Content-type: application/json"})
        @POST("/v3/conversations/{conversation_id}/mark_as_seen")
        Completable markConversationAsSeen(@Path("conversation_id") int conversationId, @QueryMap Map<String, Object> params);

        @Headers({"Content-type: application/json"})
        @POST("/v3/conversations/{conversation_id}/update_viewed_count")
        Completable updateConversationViewedCount(@Path("conversation_id") int conversationId, @QueryMap Map<String, Object> options);

        @Headers({"Content-type: application/json"})
        @POST("/v3/conversations/{conversation_id}/enable_receiving_media")
        Flowable<ConversationSettingsResponse> enableConversationMedia(@Path("conversation_id") int conversationId, @QueryMap Map<String, Object> params);

        @Headers({"Content-type: application/json"})
        @POST("/v3/conversations/{conversation_id}/disable_receiving_media")
        Flowable<ConversationSettingsResponse> disableConversationMedia(@Path("conversation_id") int conversationId, @QueryMap Map<String, Object> params);

        @Headers({"Content-type: application/json"})
        @POST("/v3/conversation_prompts/{prompt_id}/accept_asking_media_permission")
        Flowable<ConversationSettingsResponse> acceptConversationMediaPrompt(@Path("prompt_id") int promptId, @QueryMap Map<String, Object> params);

        @Headers({"Content-type: application/json"})
        @POST("/v3/conversation_prompts/{prompt_id}/reject_asking_media_permission")
        Flowable<ConversationSettingsResponse> rejectConversationMediaPrompt(@Path("prompt_id") int promptId, @QueryMap Map<String, Object> params);

        @Headers({"Content-type: application/json"})
        @POST("/v3/conversation_prompts/{prompt_id}/mark_as_read")
        Completable markConversationMediaPromptAsRead(@Path("prompt_id") int promptId, @QueryMap Map<String, Object> params);

        @Headers({"Content-type: application/json"})
        @GET("/v3/users/{inbox_user_id}/number_of_unread")
        Flowable<GetUnreadConversationCountResponse> getNumberOfUnreadConversations(@Path("inbox_user_id") int inboxUserId,
                                                                                    @QueryMap Map<String, Object> params);

        @Headers({"Content-type: application/json"})
        @GET("/v3/users/{inbox_user_id}/number_of_remaining_direct_conversations_to_create_today")
        Flowable<RemainingDirectConversationsCanCreateResponse> getRemainingDirectConversationsCanCreate(
                @Path("inbox_user_id") int inboxUserId, @QueryMap Map<String, Object> options);
        //</editor-fold>

        //<editor-fold desc="Message">
        @Headers({"Content-type: application/json"})
        @GET("/v3/messages")
        Flowable<GetMessagesResponse> getMessages(@QueryMap Map<String, Object> params);

        @Headers({"Content-type: application/json"})
        @POST("/v4/messages")
        Flowable<SendMessageResponse> sendMessageText(@Body SendMessageText body);

        @Headers({"Content-type: application/json"})
        @POST("/v4/messages")
        Flowable<SendMessageResponse> sendMessageGiphy(@Body SendMessageGiphy body);

        @Headers({"Content-type: application/json"})
        @POST("/v4/messages")
        Flowable<SendMessageResponse> sendMessagePhoto(@Body SendMessagePhoto body, @QueryMap Map<String, Object> options);

        @Headers({"Content-type: application/json"})
        @POST("/v4/messages")
        Flowable<SendMessageResponse> sendMessageVideo(@Body SendMessageVideo body, @QueryMap Map<String, Object> options);

        @Headers({"Content-type: application/json"})
        @POST("/v4/messages")
        Flowable<SendMessageResponse> sendMessageAudio(@Body SendMessageAudio body, @QueryMap Map<String, Object> options);

        @Headers({"Content-type: application/json"})
        @POST("/v3/messages/{message_id}/broadcast")
        Completable requestBroadcastNewMessage(@Path("message_id") long messageId, @Body BroadcastMessage request);

        @Headers({"Content-type: application/json"})
        @DELETE("/v3/messages/{message_id}")
        Completable deleteMessage(@Path("message_id") long messageId, @QueryMap Map<String, Object> options);

        @Headers({"Content-type: application/json"})
        @DELETE("/v3/conversations/{conversation_id}")
        Completable deleteMessages(@Path("conversation_id") int conversationId, @QueryMap Map<String, Object> options);

        @Headers({"Content-type: application/json"})
        @POST("/v3/messages/{message_id}/seen")
        Completable sendReadFlashMessage(@Path("message_id") long messageId, @QueryMap Map<String, Object> options);

        @Headers({"Content-type: application/json"})
        @POST("/v4/messages/{message_id}/flag")
        Completable reportMessage(@Path("message_id") long messageId, @Body ReportMessage reportMessage);
        //</editor-fold>

        //<editor-fold desc="User Interaction">
        @Headers({"Content-type: application/json"})
        @POST("/v3/public_users/{blocked_user_id}/block_user")
        Flowable<InboxConversation> blockUser(@Path("blocked_user_id") int friendInboxUserId, @QueryMap Map<String, Object> options);

        @Headers({"Content-type: application/json"})
        @POST("/v3/public_users/{unblocked_user_id}/unblock_user")
        Flowable<InboxConversation> unblockUser(@Path("unblocked_user_id") int friendInboxUserId, @QueryMap Map<String, Object> options);

        @Headers({"Content-type: application/json"})
        @GET("/v3/users/{inbox_user_id}/can_send_message")
        Flowable<CanSendMessageResponse> canSendMessage(@Path("inbox_user_id") int inboxUserId, @QueryMap Map<String, Object> options);
        //</editor-fold>
    }

    public interface InboxS3Api {
        @Multipart
        @POST("{s3UploadURL}")
        Completable uploadMediaToS3(@Path(value = "s3UploadURL", encoded = true) String s3UploadURL,
                                    @PartMap Map<String, RequestBody> requestBodyMap);
    }
}
