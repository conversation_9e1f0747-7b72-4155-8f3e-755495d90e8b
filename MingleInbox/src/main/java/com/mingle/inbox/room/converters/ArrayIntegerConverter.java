package com.mingle.inbox.room.converters;

import androidx.room.TypeConverter;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

public class ArrayIntegerConverter {

    @TypeConverter
    public int[] fromCreditProductString(String value) {
        if (value != null) {
            return new Gson().fromJson(value, new TypeToken<int[]>() {
            }.getType());
        }
        return null;
    }

    @TypeConverter
    public String fromArrayListCreditProduct(int[] list) {
        if (list != null) {
            return new Gson().toJson(list);
        }
        return null;
    }
}
