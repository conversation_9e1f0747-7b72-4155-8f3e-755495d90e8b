package com.mingle.inbox.room.converters;

import androidx.room.TypeConverter;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mingle.inbox.model.InboxConversationPrompt;

import java.util.ArrayList;

public class InboxConversationPromptListConverter {

    @TypeConverter
    public ArrayList<InboxConversationPrompt> fromInboxConversationPromptsString(String value) {
        if (value != null) {
            return new Gson().fromJson(value, new TypeToken<ArrayList<InboxConversationPrompt>>() {
            }.getType());
        }
        return null;
    }

    @TypeConverter
    public String fromArrayListData(ArrayList<InboxConversationPrompt> list) {
        if (list != null) {
            return new Gson().toJson(list);
        }
        return null;
    }
}
