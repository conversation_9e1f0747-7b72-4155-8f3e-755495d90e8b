package com.mingle.inbox.room

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.mingle.inbox.model.InboxConversation
import com.mingle.inbox.model.InboxMessageReported
import io.reactivex.Single

@Dao
interface InboxDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(inboxConversation: InboxConversation): Long?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(inboxConversation: List<InboxConversation>)

    @Query("select * from inbox_conversation")
    fun queryList(): Single<List<InboxConversation>>

    @Query("select * from inbox_conversation where id=:id")
    fun getLocalConversationById(id: Int): InboxConversation?

    @Query("Delete from inbox_conversation where id=:inboxConversationId")
    fun delete(inboxConversationId: Int)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(inboxMessageReported: InboxMessageReported): Long?

    @Query("SELECT EXISTS(SELECT * FROM inbox_message_reported WHERE messageId=:messageId)")
    fun isMessageReported(messageId: Long): Boolean
}
