package com.mingle.inbox.room.converters;

import androidx.room.TypeConverter;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mingle.inbox.model.InboxUser;

import java.util.ArrayList;

public class InboxListConverter {

    @TypeConverter
    public ArrayList<InboxUser> fromInboxUsersString(String value) {
        if (value != null) {
            return new Gson().fromJson(value, new TypeToken<ArrayList<InboxUser>>() {
            }.getType());
        }
        return null;
    }

    @TypeConverter
    public String fromArrayListData(ArrayList<InboxUser> list) {
        if (list != null) {
            return new Gson().toJson(list);
        }
        return null;
    }
}
