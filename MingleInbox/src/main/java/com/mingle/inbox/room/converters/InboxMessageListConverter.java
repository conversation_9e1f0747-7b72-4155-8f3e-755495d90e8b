package com.mingle.inbox.room.converters;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mingle.inbox.model.InboxMessage;

import java.util.ArrayList;

import androidx.room.TypeConverter;

public class InboxMessageListConverter {

    @TypeConverter
    public ArrayList<InboxMessage> fromInboxMessagesString(String value) {
        if (value != null) {
            return new Gson().fromJson(value, new TypeToken<ArrayList<InboxMessage>>() {
            }.getType());
        }
        return null;
    }

    @TypeConverter
    public String fromArrayListData(ArrayList<InboxMessage> list) {
        if (list != null) {
            return new Gson().toJson(list);
        }
        return null;
    }
}
