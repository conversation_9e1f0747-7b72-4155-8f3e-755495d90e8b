package com.mingle.inbox.room

import android.content.Context
import androidx.room.Database
import androidx.room.Room.databaseBuilder
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.mingle.inbox.model.InboxConversation
import com.mingle.inbox.model.InboxMessageReported
import com.mingle.inbox.room.converters.ArrayIntegerConverter
import com.mingle.inbox.room.converters.InboxListConverter
import com.mingle.inbox.room.converters.InboxMessageListConverter
import com.mingle.inbox.room.converters.InboxSeenTimeListConverter
import kotlin.concurrent.Volatile

@Database(
    entities = [InboxConversation::class, InboxMessageReported::class],
    version = 16,
    exportSchema = false
)
@TypeConverters(
    ArrayIntegerConverter::class,
    InboxListConverter::class,
    InboxMessageListConverter::class,
    InboxSeenTimeListConverter::class
)
abstract class InboxDatabase : RoomDatabase() {

    abstract fun dao(): InboxDao

    companion object {
        @Volatile
        private var INSTANCE: InboxDatabase? = null

        fun getInstance(context: Context): InboxDatabase {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: buildDatabase(context).also { INSTANCE = it }
            }
        }

        private fun buildDatabase(context: Context): InboxDatabase {
            return databaseBuilder(context.applicationContext, InboxDatabase::class.java, "twine_inbox")
                .fallbackToDestructiveMigration(true)
                .build()
        }
    }
}

