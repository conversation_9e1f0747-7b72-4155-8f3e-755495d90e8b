package com.mingle.inbox.room.converters;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mingle.inbox.model.InboxSeenTime;

import java.util.ArrayList;

import androidx.room.TypeConverter;

public class InboxSeenTimeListConverter {

    @TypeConverter
    public ArrayList<InboxSeenTime> fromInboxSeenTimesString(String value) {
        if (value != null) {
            return new Gson().fromJson(value, new TypeToken<ArrayList<InboxSeenTime>>() {
            }.getType());
        }
        return null;
    }

    @TypeConverter
    public String fromArrayListData(ArrayList<InboxSeenTime> list) {
        if (list != null) {
            return new Gson().toJson(list);
        }
        return null;
    }
}
