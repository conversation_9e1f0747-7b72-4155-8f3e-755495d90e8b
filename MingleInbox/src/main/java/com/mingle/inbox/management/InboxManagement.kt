package com.mingle.inbox.management

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.media.ThumbnailUtils
import android.provider.MediaStore
import android.text.TextUtils
import androidx.annotation.WorkerThread
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.google.android.gms.common.util.CollectionUtils
import com.google.gson.JsonParser
import com.mingle.global.extensions.optBoolean
import com.mingle.global.extensions.optInt
import com.mingle.global.extensions.optString
import com.mingle.global.model.response.S3PreSignedPost
import com.mingle.global.utils.FileUtil
import com.mingle.global.utils.OkhttpUtil.Companion.formDataContentType
import com.mingle.global.utils.OkhttpUtil.Companion.getRequestBodyLinkedMap
import com.mingle.global.utils.OkhttpUtil.Companion.imageContentType
import com.mingle.global.utils.S3Util
import com.mingle.global.utils.date.DateTimeUtil
import com.mingle.inbox.constants.Constants
import com.mingle.inbox.constants.ConversationStatus
import com.mingle.inbox.interfaces.InboxGetConversationCallback
import com.mingle.inbox.interfaces.InboxManagementCallback
import com.mingle.inbox.model.InboxConversation
import com.mingle.inbox.model.InboxConversationPrompt
import com.mingle.inbox.model.InboxMessage
import com.mingle.inbox.model.InboxMessageReported
import com.mingle.inbox.model.InboxSeenTime
import com.mingle.inbox.model.InboxUser
import com.mingle.inbox.model.credentials.InboxCredentials
import com.mingle.inbox.model.eventbus.InboxConversationRequestResolved
import com.mingle.inbox.model.eventbus.InboxLocalConversationsLoaded
import com.mingle.inbox.model.eventbus.InboxNullEvent
import com.mingle.inbox.model.eventbus.InboxUnreadMessagesCountChangedEvent
import com.mingle.inbox.model.eventbus.net.InboxBaseEvent
import com.mingle.inbox.model.eventbus.net.InboxBlockUserEvent
import com.mingle.inbox.model.eventbus.net.InboxCanSendMessageEvent
import com.mingle.inbox.model.eventbus.net.InboxConversationMediaChangedEvent
import com.mingle.inbox.model.eventbus.net.InboxDeleteConversationEvent
import com.mingle.inbox.model.eventbus.net.InboxDeleteMessageEvent
import com.mingle.inbox.model.eventbus.net.InboxGetConversationEvent
import com.mingle.inbox.model.eventbus.net.InboxGetLatestMessageEvent
import com.mingle.inbox.model.eventbus.net.InboxGetMoreMessageEvent
import com.mingle.inbox.model.eventbus.net.InboxGetNewConversationsEvent
import com.mingle.inbox.model.eventbus.net.InboxGetOlderConversationsEvent
import com.mingle.inbox.model.eventbus.net.InboxMarkConversationAsSeenEvent
import com.mingle.inbox.model.eventbus.net.InboxMarkConversationMediaPromptAsReadEvent
import com.mingle.inbox.model.eventbus.net.InboxSendMessageEvent
import com.mingle.inbox.model.eventbus.net.InboxSendReadFlashMessageEvent
import com.mingle.inbox.model.eventbus.net.InboxUnblockUserEvent
import com.mingle.inbox.model.eventbus.net.InboxUpdateConversationStatusEvent
import com.mingle.inbox.model.eventbus.net.InboxUpdateEncounterLikeEvent
import com.mingle.inbox.model.eventbus.net.InboxUpdateProfileForConversationEvent
import com.mingle.inbox.model.eventbus.net.InboxUpdateProfileForConversationsEvent
import com.mingle.inbox.model.eventbus.net.InboxUploadAudioEvent
import com.mingle.inbox.model.eventbus.net.InboxUploadPhotosEvent
import com.mingle.inbox.model.eventbus.net.InboxUploadVideoEvent
import com.mingle.inbox.model.eventbus.net.NewConversationEvent
import com.mingle.inbox.model.eventbus.pusher.InboxConversationPromptCreatedEvent
import com.mingle.inbox.model.eventbus.pusher.InboxConversationSeenEvent
import com.mingle.inbox.model.eventbus.pusher.InboxMessageCreatedEvent
import com.mingle.inbox.model.eventbus.pusher.InboxMessageDeletedEvent
import com.mingle.inbox.model.eventbus.pusher.InboxRewindEncounterLikeEvent
import com.mingle.inbox.model.request.BaseRequest
import com.mingle.inbox.model.request.DeleteMessages
import com.mingle.inbox.model.request.GetLatestMessages
import com.mingle.inbox.model.request.GetMoreMessages
import com.mingle.inbox.model.request.GetNewConversations
import com.mingle.inbox.model.request.GetOlderConversations
import com.mingle.inbox.model.request.UpdateConversationStatus
import com.mingle.inbox.model.request.UpdateConversationViewedCount
import com.mingle.inbox.model.request.message.BroadcastMessage
import com.mingle.inbox.model.request.message.ReportMessage
import com.mingle.inbox.model.request.message.SendMessageAudio
import com.mingle.inbox.model.request.message.SendMessageBase
import com.mingle.inbox.model.request.message.SendMessageGiphy
import com.mingle.inbox.model.request.message.SendMessagePhoto
import com.mingle.inbox.model.request.message.SendMessageText
import com.mingle.inbox.model.request.message.SendMessageVideo
import com.mingle.inbox.model.response.CanSendMessageResponse
import com.mingle.inbox.model.response.GetMessagesResponse
import com.mingle.inbox.model.response.GetUnreadConversationCountResponse
import com.mingle.inbox.model.response.RemainingDirectConversationsCanCreateResponse
import com.mingle.inbox.model.response.SendMessageResponse
import com.mingle.inbox.net.RetrofitConnector
import com.mingle.inbox.utils.InboxUtils
import com.uber.autodispose.AutoDispose
import com.uber.autodispose.ScopeProvider
import io.reactivex.Completable
import io.reactivex.Flowable
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.launch
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus
import retrofit2.HttpException
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File
import java.lang.ref.WeakReference
import java.util.Objects

class InboxManagement(
    context: Context, isStaging: Boolean, stagingAuthorizationUser: String?, stagingAuthorizationPassword: String?,
    inboxServerAddress: String, inboxServerToken: String, inboxServerSalt: String, callback: InboxManagementCallback
) {
    private val inboxCredentials = InboxCredentials(
        isStaging, stagingAuthorizationUser, stagingAuthorizationPassword,
        inboxServerAddress, inboxServerToken, inboxServerSalt
    )
    private val inboxManagementCallback: InboxManagementCallback = callback
    private lateinit var roomManagement: InboxRoomManagement
    private lateinit var retrofitConnector: RetrofitConnector

    private val contextRef = WeakReference(context)
    private var inboxUserId = 0
    private lateinit var appName: String
    private lateinit var mainDeviceId: String
    private lateinit var languagePreference: String
    private lateinit var lifecycleOwnerRef: WeakReference<LifecycleOwner>

    var isInitialized: Boolean = false
        private set

    private var inboxConversations = ArrayList<InboxConversation>()

    var numberOfUnreadConversations: Int = 0
        private set
    var numberOfConversationsRequest: Int = 0
        private set
    var numberOfUnreadConversationsRequest: Int = 0
        private set
    var remainingDirectConversationsCanCreate: Int = 0
        private set

    private val conversationsNeedToNotify: MutableList<Int> = ArrayList()

    fun initInbox(lifecycleOwner: LifecycleOwner, appName: String, inboxUserId: Int, deviceId: String, authToken: String, languagePreference: String, cloudProjectNumber: Long) {
        this.appName = appName
        this.inboxUserId = inboxUserId
        this.mainDeviceId = deviceId
        this.languagePreference = languagePreference
        this.lifecycleOwnerRef = WeakReference(lifecycleOwner)
        contextRef.get()?.let {
            retrofitConnector = RetrofitConnector(it, inboxCredentials, authToken, cloudProjectNumber)
            roomManagement = InboxRoomManagement(it)
            roomManagement.conversationsFromDatabase
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.computation())
                .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
                .subscribe({ cachedConversations: List<InboxConversation> ->
                    lifecycleOwnerRef.get()?.lifecycleScope?.launch {
                        inboxConversations.clear()
                        inboxConversations.addAll(cachedConversations)
                        InboxUtils.sortConversationsByTime(inboxConversations)
                        EventBus.getDefault().post(InboxLocalConversationsLoaded())

                        requestNumberOfUnreadConversations()
                        requestRemainingDirectConversationsCanCreate()
                        if (conversations.none { it -> !it.isConversationRequest }) {
                            getOlderConversations()
                        } else {
                            getNewConversations()
                        }
                        if (conversations.none { it -> it.isConversationRequest }) {
                            getOlderConversations(true)
                        } else {
                            getNewConversations(true)
                        }
                    }
                }, { obj: Throwable -> obj.printStackTrace() })
            isInitialized = true
        }
    }

    fun reloadActionTokenSettings() {
        retrofitConnector.actionTokenInterceptor.reloadActionTokenSettings()
    }

    fun resetInbox() {
        isInitialized = false
        releaseResources()
        roomManagement.deleteDatabase()
    }

    fun releaseResources() {
        inboxUserId = 0
        mainDeviceId = ""
        languagePreference = ""
        inboxConversations = ArrayList()
        isInitialized = false
    }

    fun setLanguagePreference(languagePreference: String) {
        this.languagePreference = languagePreference
    }

    @get:Synchronized
    val conversations: ArrayList<InboxConversation>
        get() {
            return inboxConversations
        }

    @WorkerThread
    fun getLocalConversation(conversationId: Int): InboxConversation? {
        if (conversations.isNotEmpty()) {
            return InboxUtils.getConversation(conversationId, conversations)
        }
        return roomManagement.getLocalConversation(conversationId)
    }

    fun getLocalConversationByInboxUserId(inboxUserId: Int): InboxConversation? {
        return InboxUtils.getConversationByInboxUserId(inboxUserId, conversations)
    }

    fun getFriendInboxUser(conversation: InboxConversation?): InboxUser? {
        return InboxUtils.getFriendInboxUser(conversation, inboxUserId)
    }

    fun updateFriendInboxUserStatus(conversation: InboxConversation?, userStatus: String?) {
        if (conversation != null) {
            val friendInboxUser = InboxUtils.getFriendInboxUser(conversation, inboxUserId)
            if (friendInboxUser != null) {
                friendInboxUser.userStatus = userStatus
                if (friendInboxUser.isBanned || friendInboxUser.isScammed || friendInboxUser.isSpammer || friendInboxUser.isDeleted) {
                    deleteLocalConversation(conversation.id)
                } else {
                    saveConversation(conversation)
                }
            }
        }
    }

    private fun getFriendInboxUsers(conversation: InboxConversation?): ArrayList<InboxUser> {
        return InboxUtils.getFriendInboxUsers(conversation, inboxUserId)
    }

    fun getMyInboxUser(conversation: InboxConversation?): InboxUser? {
        return InboxUtils.getMyInboxUser(conversation, inboxUserId)
    }

    @WorkerThread
    fun getInboxUserIds(conversationId: Int): ArrayList<Int> {
        val inboxUserIds = ArrayList<Int>()
        val conversation = getLocalConversation(conversationId)
        if (conversation != null) {
            for (j in conversation.users.indices) {
                if (!inboxUserIds.contains(conversation.users[j].inboxUserId)) {
                    inboxUserIds.add(conversation.users[j].inboxUserId)
                }
            }
        }
        return inboxUserIds
    }

    private fun getAllInboxUserIds(listConversation: ArrayList<InboxConversation>): ArrayList<Int> {
        val inboxUserIds = ArrayList<Int>()
        for (i in listConversation.indices) {
            val inboxConversation = listConversation[i]
            for (j in inboxConversation.users.indices) {
                if (!inboxUserIds.contains(inboxConversation.users[j].inboxUserId)) {
                    inboxUserIds.add(inboxConversation.users[j].inboxUserId)
                }
            }
        }
        return inboxUserIds
    }

    private fun getConversationIds(listConversation: ArrayList<InboxConversation>): ArrayList<Int> {
        val listConversationId = ArrayList<Int>()
        for (i in listConversation.indices) {
            listConversationId.add(listConversation[i].id)
        }
        return listConversationId
    }

    private fun getLastConversation(conversations: ArrayList<InboxConversation>): InboxConversation? {
        for (i in conversations.indices.reversed()) {
            if (!TextUtils.isEmpty(conversations[i].lastMessageCreatedAt)) {
                return conversations[i]
            }
        }
        return null
    }

    fun getNewConversations(conversationRequest: Boolean = false) {
        // guard against invalid state
        if (contextRef.get() == null || inboxUserId <= 0 || CollectionUtils.isEmpty(conversations)) {
            EventBus.getDefault().post(
                InboxGetNewConversationsEvent(conversationRequest).apply { status = InboxBaseEvent.FAILED })
            return
        }

        Flowable
            .fromCallable {
                val request = GetNewConversations(appName, inboxUserId, mainDeviceId, languagePreference)
                request.conversationRequest = conversationRequest
                conversations.firstOrNull { it.isConversationRequest == conversationRequest }
                    ?.let { firstConv ->
                        if (firstConv.messages.isNotEmpty()) {
                            val latestMessageId = firstConv.messages.last().id
                            request.latestMessageId = latestMessageId.toString()
                        }
                    }
                request
            }
            .flatMap { request: GetNewConversations -> retrofitConnector.inboxApi.getNewConversations(request.queryMap) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe({ inboxConversations: ArrayList<InboxConversation> ->
                val event = InboxGetNewConversationsEvent(conversationRequest)
                if (!CollectionUtils.isEmpty(inboxConversations)) {
                    val insertedInboxConversations = insertConversations(inboxConversations)
                    InboxUtils.sortConversationsByTime(conversations)
                    requestUpdateUserProfile(inboxConversations)

                    if (!CollectionUtils.isEmpty(insertedInboxConversations)) {
                        roomManagement.saveInboxConversations(insertedInboxConversations)
                        requestRemainingDirectConversationsCanCreate()
                    }

                    event.status = InboxBaseEvent.SUCCESS
                } else {
                    event.status = InboxBaseEvent.FAILED
                }
                EventBus.getDefault().post(event)
            }, { throwable: Throwable? ->
                EventBus.getDefault().post(
                    InboxGetNewConversationsEvent(conversationRequest).apply {
                        status = InboxBaseEvent.FAILED
                        this.throwable = throwable
                    }
                )
            })
    }

    fun getOlderConversations(conversationRequest: Boolean = false) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxGetOlderConversationsEvent(conversationRequest).apply { status = InboxBaseEvent.FAILED })
            return
        }

        Flowable
            .fromCallable {
                val request = GetOlderConversations(appName, inboxUserId, mainDeviceId, languagePreference)
                request.conversationRequest = conversationRequest
                conversations.filter { it.isConversationRequest == conversationRequest }.toCollection(ArrayList()).let {
                    getLastConversation(it)?.lastMessageCreatedAt?.let { lastMessageCreatedAt -> request.lastMessageCreatedAt = lastMessageCreatedAt }
                }
                request
            }
            .flatMap { request -> retrofitConnector.inboxApi.getOlderConversations(request.queryMap) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe({ inboxConversations ->
                val event = InboxGetOlderConversationsEvent(conversationRequest).apply {
                    status = if (inboxConversations.isNotEmpty()) {
                        // insert, sort, update profile & room, then fire notifications
                        val inserted = insertConversations(inboxConversations)
                        InboxUtils.sortConversationsByTime(conversations)
                        requestUpdateUserProfile(inboxConversations)
                        if (inserted.isNotEmpty()) {
                            roomManagement.saveInboxConversations(inserted)
                            requestRemainingDirectConversationsCanCreate()
                            InboxBaseEvent.SUCCESS
                        } else {
                            InboxBaseEvent.FAILED
                        }
                    } else {
                        InboxBaseEvent.FAILED
                    }
                }
                EventBus.getDefault().post(event)
            }, { throwable ->
                val event = InboxGetOlderConversationsEvent(conversationRequest).apply {
                    status = InboxBaseEvent.FAILED
                    this.throwable = throwable
                }
                EventBus.getDefault().post(event)
            })
    }

    private fun requestNumberOfUnreadConversations() {
        if (contextRef.get() == null || inboxUserId <= 0) {
            return
        }
        val request = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        retrofitConnector.inboxApi.getNumberOfUnreadConversations(inboxUserId, request.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe({ unreadCount: GetUnreadConversationCountResponse ->
                numberOfUnreadConversations = unreadCount.unreadCount
                numberOfConversationsRequest = unreadCount.conversationRequestCount
                numberOfUnreadConversationsRequest = unreadCount.unreadConversationRequestCount
                EventBus.getDefault().post(InboxUnreadMessagesCountChangedEvent())
            }, { obj: Throwable -> obj.printStackTrace() })
    }


    private fun requestRemainingDirectConversationsCanCreate() {
        if (contextRef.get() == null || inboxUserId <= 0) {
            return
        }
        val request = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        retrofitConnector.inboxApi.getRemainingDirectConversationsCanCreate(inboxUserId, request.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe(
                { response: RemainingDirectConversationsCanCreateResponse -> remainingDirectConversationsCanCreate = response.count },
                { t: Throwable -> Timber.e(t) })
    }

    private fun insertConversations(newInboxConversations: ArrayList<InboxConversation>): ArrayList<InboxConversation> {
        val insertedInboxConversations = ArrayList<InboxConversation>()
        for (newConversation in newInboxConversations) {
            if (!inboxConversations.contains(newConversation)) {
                inboxConversations.add(newConversation)
                InboxUtils.sortMessagesById(newConversation.messages)
                insertedInboxConversations.add(newConversation)
            } else {
                for (oldInboxConversation in inboxConversations) {
                    if (oldInboxConversation.id == newConversation.id) {
                        updateConversationInfo(oldInboxConversation, newConversation)
                        InboxUtils.sortMessagesById(oldInboxConversation.messages)
                        insertedInboxConversations.add(oldInboxConversation)
                        break
                    }
                }
            }
        }
        return insertedInboxConversations
    }

//    private fun updateConversation(inboxMessage: InboxMessage, inboxUserId: Int, numRetry: Int) {
//        if (mContextReference.get() == null || inboxUserId <= 0) {
//            EventBus.getDefault().post(InboxNullEvent())
//            return
//        }
//        val request = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
//        retrofitConnector.inboxApi.getConversation(inboxMessage.conversationId, request.queryMap)
//            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
//            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
//            .subscribe({ conversation: InboxConversation ->
//                val oldConversation = getLocalConversation(conversation.id)
//                if (oldConversation != null && oldConversation.id != 0) {
//                    updateConversationInfo(oldConversation, conversation)
//                    InboxUtils.sortMessagesById(oldConversation.messages)
//                    updateSeenMessageIds(oldConversation)
//                    roomManagement.saveInboxConversation(oldConversation)
//                    requestUpdateUserProfile(oldConversation.id, false)
//                }
//            }, {
//                if (numRetry < MAX_NUM_RETRY) {
//                    updateConversation(inboxMessage, inboxUserId, numRetry + 1)
//                }
//            })
//    }

    private fun updateConversationInfo(oldConversation: InboxConversation, newConversation: InboxConversation) {
        oldConversation.name = newConversation.name
        oldConversation.status = newConversation.status
        oldConversation.lastMessageCreatedAt = newConversation.lastMessageCreatedAt
        oldConversation.seenTimestamp = newConversation.seenTimestamp
        oldConversation.newMessagesCount = newConversation.newMessagesCount
        oldConversation.conversationSource = newConversation.conversationSource
        oldConversation.compatibilityDescription = newConversation.compatibilityDescription
        oldConversation.encounterLikeByUserIds = newConversation.encounterLikeByUserIds
        oldConversation.isConversationRequest = newConversation.isConversationRequest
        oldConversation.conversationPrompts = newConversation.conversationPrompts

        val messages = newConversation.messages
        for (message in messages) {
            insertMessage(oldConversation, message)
        }
        updateUpdatedUser(oldConversation, oldConversation.users, newConversation.users)
    }

    private fun updateUpdatedUser(oldConversation: InboxConversation, oldUsers: ArrayList<InboxUser>?, newUsers: ArrayList<InboxUser>?) {
        if (newUsers == null) {
            return
        }

        if (oldUsers == null) {
            oldConversation.users = newUsers
            return
        }

        for (oldCount in oldUsers.indices) {
            for (newCount in newUsers.indices) {
                val oldUser = oldUsers[oldCount]
                val newUser = newUsers[newCount]

                if (oldUser.inboxUserId == newUser.inboxUserId) {
                    // set data from server
                    oldUser.name = newUser.name
                    oldUser.userIdentity = newUser.userIdentity
                    oldUser.isVerified = newUser.isVerified
                    oldUser.isMatched = newUser.isMatched
                    oldUser.receivingMediaEnabled = newUser.receivingMediaEnabled

                    // if old user id == 0 && empty photo => update from newUser.
                    if (oldUser.id == 0 && TextUtils.isEmpty(oldUser.profilePhotoUrl)) {
                        oldUser.id = newUser.id
                        oldUser.profilePhotoUrl = newUser.profilePhotoUrl
                    }
                }
            }
        }
    }

    private fun refreshConversation(conversationId: Int, isSystemMessage: Boolean, numRetry: Int) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        val request = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        retrofitConnector.inboxApi.getConversation(conversationId, request.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe({ conversation: InboxConversation? ->
                if (conversation != null) {
                    val messages = conversation.messages
                    InboxUtils.sortMessagesById(messages)
                    val oldConversation = getLocalConversation(conversationId)
                    if (oldConversation != null && oldConversation.id != 0) {
                        val isUnreadOldConversation = InboxUtils.isUnreadConversation(oldConversation)
                        updateConversationInfo(oldConversation, conversation)
                        InboxUtils.sortMessagesById(oldConversation.messages)
                        if (!isUnreadOldConversation && InboxUtils.isUnreadConversation(conversation)) {
                            // update unread message
                            numberOfUnreadConversations += 1
                            if (conversation.isConversationRequest) {
                                numberOfUnreadConversationsRequest += 1
                            }
                            EventBus.getDefault().post(InboxUnreadMessagesCountChangedEvent())
                        }
                        roomManagement.saveInboxConversation(oldConversation)
                        conversationsNeedToNotify.remove(oldConversation.id)
                        checkToSendMessageNotification(conversationId, conversation.isConversationRequest, isSystemMessage)
                    } else {
                        if (!conversations.contains(conversation)) {
                            conversations.add(0, conversation)
                        }
                        requestNumberOfUnreadConversations()
                        requestUpdateUserProfile(conversation.id, isSystemMessage)
                        roomManagement.saveInboxConversation(conversation)
                    }
                    InboxUtils.sortConversationsByTime(conversations)

                    val event = NewConversationEvent(conversationId)
                    event.status = InboxBaseEvent.SUCCESS
                    EventBus.getDefault().post(event)
                }
            }, {
                if (numRetry < MAX_NUM_RETRY) {
                    refreshConversation(conversationId, isSystemMessage, numRetry + 1)
                } else {
                    val event = NewConversationEvent(conversationId)
                    event.status = InboxBaseEvent.FAILED
                    EventBus.getDefault().post(event)
                }
            })
    }

    fun getConversation(conversationId: Int, callback: InboxGetConversationCallback?) {
        val request = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        retrofitConnector.inboxApi.getConversation(conversationId, request.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe({ inboxConversation: InboxConversation ->
                val oldConversation = getLocalConversation(conversationId)
                if (oldConversation != null && oldConversation.id != 0) {
                    val isUnreadOldConversation = InboxUtils.isUnreadConversation(oldConversation)
                    updateConversationInfo(oldConversation, inboxConversation)
                    InboxUtils.sortMessagesById(oldConversation.messages)
                    if (!isUnreadOldConversation && InboxUtils.isUnreadConversation(inboxConversation)) {
                        // update unread message
                        numberOfUnreadConversations += 1
                        if (inboxConversation.isConversationRequest) {
                            numberOfUnreadConversationsRequest += 1
                        }
                        EventBus.getDefault().post(InboxUnreadMessagesCountChangedEvent())
                    }
                    roomManagement.saveInboxConversation(oldConversation)
                    InboxUtils.sortConversationsByTime(conversations)
                    callback?.onSuccess(oldConversation)
                } else {
                    InboxUtils.sortMessagesById(inboxConversation.messages)
                    conversations.add(0, inboxConversation)
                    requestNumberOfUnreadConversations()
                    requestUpdateUserProfile(inboxConversation.id, false)
                    roomManagement.saveInboxConversation(inboxConversation)
                    InboxUtils.sortConversationsByTime(conversations)
                    callback?.onSuccess(inboxConversation)
                }
            }, { throwable: Throwable? -> callback?.onError(throwable) })
    }

    fun getConversationByInboxUserId(targetInboxUserId: Int, callback: InboxGetConversationCallback) {
        if (contextRef.get() == null || inboxUserId <= 0 || targetInboxUserId <= 0) {
            val inboxGetConversationEvent = InboxGetConversationEvent()
            inboxGetConversationEvent.status = InboxGetConversationEvent.NULL
            inboxGetConversationEvent.targetInboxUserId = targetInboxUserId
            EventBus.getDefault().post(inboxGetConversationEvent)
            callback.onError(IllegalArgumentException("Invalid inboxUserId or targetInboxUserId"))
            return
        }
        val request = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        retrofitConnector.inboxApi.getConversationByInboxUserId(targetInboxUserId, request.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe({ inboxConversation: InboxConversation ->
                val oldConversation = getLocalConversationByInboxUserId(targetInboxUserId)
                if (oldConversation != null && oldConversation.id != 0) {
                    val isUnreadOldConversation = InboxUtils.isUnreadConversation(oldConversation)
                    updateConversationInfo(oldConversation, inboxConversation)
                    InboxUtils.sortMessagesById(oldConversation.messages)
                    if (!isUnreadOldConversation && InboxUtils.isUnreadConversation(inboxConversation)) {
                        numberOfUnreadConversations += 1
                        if (inboxConversation.isConversationRequest) {
                            numberOfUnreadConversationsRequest += 1
                        }
                        EventBus.getDefault().post(InboxUnreadMessagesCountChangedEvent())
                    }
                    roomManagement.saveInboxConversation(oldConversation)
                    InboxUtils.sortConversationsByTime(conversations)
                    callback.onSuccess(oldConversation)
                } else {
                    InboxUtils.sortMessagesById(inboxConversation.messages)
                    if (!conversations.contains(inboxConversation)) {
                        conversations.add(0, inboxConversation)
                    }
                    requestNumberOfUnreadConversations()
                    requestUpdateUserProfile(inboxConversation.id, false)
                    roomManagement.saveInboxConversation(inboxConversation)
                    InboxUtils.sortConversationsByTime(conversations)
                    callback.onSuccess(inboxConversation)
                }
                val inboxGetConversationEvent = InboxGetConversationEvent()
                inboxGetConversationEvent.status = InboxGetConversationEvent.SUCCESS
                inboxGetConversationEvent.targetInboxUserId = targetInboxUserId
                inboxGetConversationEvent.inboxConversation = inboxConversation
                EventBus.getDefault().post(inboxGetConversationEvent)
            }, { throwable: Throwable? ->
                callback.onError(throwable)
                val inboxGetConversationEvent = InboxGetConversationEvent()
                inboxGetConversationEvent.status = InboxGetConversationEvent.FAILED
                inboxGetConversationEvent.targetInboxUserId = targetInboxUserId
                EventBus.getDefault().post(inboxGetConversationEvent)
            })
    }

    fun markConversationAsSeen(conversation: InboxConversation?) {
        if (contextRef.get() == null || inboxUserId <= 0 || conversation == null) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        val request = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        retrofitConnector.inboxApi.markConversationAsSeen(conversation.id, request.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
            .subscribe({
                conversation.shouldMarkConversationAsSeen = false
                val inboxMarkConversationAsSeenEvent = InboxMarkConversationAsSeenEvent()
                inboxMarkConversationAsSeenEvent.status = InboxMarkConversationAsSeenEvent.SUCCESS
                inboxMarkConversationAsSeenEvent.conversationId = conversation.id
                EventBus.getDefault().post(inboxMarkConversationAsSeenEvent)
            }, { throwable: Throwable? ->
                val inboxMarkConversationAsSeenEvent = InboxMarkConversationAsSeenEvent()
                inboxMarkConversationAsSeenEvent.status = InboxMarkConversationAsSeenEvent.FAILED
                inboxMarkConversationAsSeenEvent.conversationId = conversation.id
                inboxMarkConversationAsSeenEvent.throwable = throwable
                EventBus.getDefault().post(inboxMarkConversationAsSeenEvent)
            })
    }

    fun updateConversationViewedCount(conversation: InboxConversation?) {
        if (contextRef.get() == null || inboxUserId <= 0 || conversation == null) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        Completable.fromAction {
            val myInboxUser = getMyInboxUser(conversation)
            myInboxUser?.let {
                val newViewedCount = conversation.getNewViewedCount(it)
                if (newViewedCount > 0) {
                    val request = UpdateConversationViewedCount(appName, inboxUserId, mainDeviceId, languagePreference)
                    request.setNewViewedCount(newViewedCount.toString())
                    retrofitConnector.inboxApi.updateConversationViewedCount(conversation.id, request.queryMap)
                        .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
                        .`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
                        .subscribe {
                            val seenTimestamp = conversation.seenTimestamp
                            if (seenTimestamp != null) {
                                for (i in seenTimestamp.indices) {
                                    if (seenTimestamp[i].inboxUserId == inboxUserId) {
                                        val seenAt = DateTimeUtil.formatDate(DateTimeUtil.now(), DateTimeUtil.defaultFormatExtended)
                                        seenTimestamp[i].seenAt = seenAt
                                        break
                                    }
                                }
                            }
                        }
                }
            }
        }.subscribe()
    }

    fun enableConversationMedia(conversation: InboxConversation?) {
        if (contextRef.get() == null || inboxUserId <= 0 || conversation == null) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        val request = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        retrofitConnector.inboxApi.enableConversationMedia(conversation.id, request.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe({
                val isReceivingMediaEnabled = it.receivingMediaEnabled
                conversation.setReceivingMediaEnabled(inboxUserId, enabled = isReceivingMediaEnabled)
                saveConversation(conversation)
                val inboxConversationMediaChangedEvent = InboxConversationMediaChangedEvent()
                inboxConversationMediaChangedEvent.status = InboxBaseEvent.SUCCESS
                inboxConversationMediaChangedEvent.conversationId = conversation.id
                inboxConversationMediaChangedEvent.isMediaSettingChanged = true
                EventBus.getDefault().post(inboxConversationMediaChangedEvent)
            }, { throwable: Throwable? ->
                val inboxConversationMediaChangedEvent = InboxConversationMediaChangedEvent()
                inboxConversationMediaChangedEvent.status = InboxBaseEvent.FAILED
                inboxConversationMediaChangedEvent.throwable = throwable
                inboxConversationMediaChangedEvent.conversationId = conversation.id
                EventBus.getDefault().post(inboxConversationMediaChangedEvent)
            })
    }

    fun disableConversationMedia(conversation: InboxConversation?) {
        if (contextRef.get() == null || inboxUserId <= 0 || conversation == null) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        val request = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        retrofitConnector.inboxApi.disableConversationMedia(conversation.id, request.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe({
                val isReceivingMediaEnabled = it.receivingMediaEnabled
                conversation.setReceivingMediaEnabled(inboxUserId, enabled = isReceivingMediaEnabled)
                saveConversation(conversation)
                val inboxConversationMediaChangedEvent = InboxConversationMediaChangedEvent()
                inboxConversationMediaChangedEvent.status = InboxBaseEvent.SUCCESS
                inboxConversationMediaChangedEvent.conversationId = conversation.id
                inboxConversationMediaChangedEvent.isMediaSettingChanged = true
                EventBus.getDefault().post(inboxConversationMediaChangedEvent)
            }, { throwable: Throwable? ->
                val inboxConversationMediaChangedEvent = InboxConversationMediaChangedEvent()
                inboxConversationMediaChangedEvent.status = InboxBaseEvent.FAILED
                inboxConversationMediaChangedEvent.throwable = throwable
                EventBus.getDefault().post(inboxConversationMediaChangedEvent)
            })
    }

    fun acceptConversationMediaPrompt(conversationId: Int, conversationPromptId: Int) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        val request = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        retrofitConnector.inboxApi.acceptConversationMediaPrompt(conversationPromptId, request.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe({
                val localConversation = getLocalConversation(conversationId)
                localConversation?.let {
                    it.setReceivingMediaEnabled(inboxUserId, true)
                    setConversationMediaPromptClosed(it.id, conversationPromptId)
                    saveConversation(localConversation)
                }
                val inboxConversationMediaChangedEvent = InboxConversationMediaChangedEvent()
                inboxConversationMediaChangedEvent.status = InboxBaseEvent.SUCCESS
                inboxConversationMediaChangedEvent.conversationId = conversationId
                inboxConversationMediaChangedEvent.conversationPromptId = conversationPromptId
                inboxConversationMediaChangedEvent.isMediaSettingChanged = true
                EventBus.getDefault().post(inboxConversationMediaChangedEvent)
            }, { throwable: Throwable? ->
                val inboxConversationMediaChangedEvent = InboxConversationMediaChangedEvent()
                inboxConversationMediaChangedEvent.status = InboxBaseEvent.FAILED
                inboxConversationMediaChangedEvent.throwable = throwable
                inboxConversationMediaChangedEvent.conversationId = conversationId
                inboxConversationMediaChangedEvent.conversationPromptId = conversationPromptId
                EventBus.getDefault().post(inboxConversationMediaChangedEvent)
            })
    }

    fun rejectConversationMediaPrompt(conversationId: Int, conversationPromptId: Int) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        val request = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        retrofitConnector.inboxApi.rejectConversationMediaPrompt(conversationPromptId, request.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe({
                val localConversation = getLocalConversation(conversationId)
                localConversation?.let {
                    it.setReceivingMediaEnabled(inboxUserId, true)
                    setConversationMediaPromptClosed(it.id, conversationPromptId)
                    saveConversation(localConversation)
                }
                val inboxConversationMediaChangedEvent = InboxConversationMediaChangedEvent()
                inboxConversationMediaChangedEvent.status = InboxBaseEvent.SUCCESS
                inboxConversationMediaChangedEvent.conversationId = conversationId
                inboxConversationMediaChangedEvent.conversationPromptId = conversationPromptId
                inboxConversationMediaChangedEvent.isMediaSettingChanged = true
                EventBus.getDefault().post(inboxConversationMediaChangedEvent)
            }, { throwable: Throwable? ->
                val inboxConversationMediaChangedEvent = InboxConversationMediaChangedEvent()
                inboxConversationMediaChangedEvent.status = InboxBaseEvent.FAILED
                inboxConversationMediaChangedEvent.throwable = throwable
                inboxConversationMediaChangedEvent.conversationId = conversationId
                inboxConversationMediaChangedEvent.conversationPromptId = conversationPromptId
                EventBus.getDefault().post(inboxConversationMediaChangedEvent)
            })
    }

    fun setConversationMediaPromptClosed(conversationId: Int, conversationPromptId: Int) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        var isMediaSettingChanged = false
        val conversation = getLocalConversation(conversationId)
        conversation?.let { it ->
            it.setConversationPromptClosed(conversationPromptId)
            it.conversationPrompts.firstOrNull { it.id == conversationPromptId }
                ?.let {
                    if (it.isAskingMediaPermission) {
                        isMediaSettingChanged = true
                    }
                }
        }
        val inboxConversationMediaChangedEvent = InboxConversationMediaChangedEvent()
        inboxConversationMediaChangedEvent.status = InboxBaseEvent.SUCCESS
        inboxConversationMediaChangedEvent.conversationId = conversationId
        inboxConversationMediaChangedEvent.conversationPromptId = conversationPromptId
        inboxConversationMediaChangedEvent.isMediaSettingChanged = isMediaSettingChanged
        EventBus.getDefault().post(inboxConversationMediaChangedEvent)
    }

    fun markConversationMediaPromptAsRead(conversationId: Int, conversationPromptId: Int) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        val request = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        retrofitConnector.inboxApi.markConversationMediaPromptAsRead(conversationPromptId, request.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
            .subscribe({
                val inboxMarkConversationMediaPromptAsReadEvent = InboxMarkConversationMediaPromptAsReadEvent()
                inboxMarkConversationMediaPromptAsReadEvent.status = InboxBaseEvent.SUCCESS
                inboxMarkConversationMediaPromptAsReadEvent.conversationId = conversationId
                inboxMarkConversationMediaPromptAsReadEvent.conversationPromptId = conversationPromptId
                EventBus.getDefault().post(inboxMarkConversationMediaPromptAsReadEvent)
            }, { throwable: Throwable? ->
                val inboxMarkConversationMediaPromptAsReadEvent = InboxMarkConversationMediaPromptAsReadEvent()
                inboxMarkConversationMediaPromptAsReadEvent.status = InboxBaseEvent.FAILED
                inboxMarkConversationMediaPromptAsReadEvent.throwable = throwable
                inboxMarkConversationMediaPromptAsReadEvent.conversationId = conversationId
                inboxMarkConversationMediaPromptAsReadEvent.conversationPromptId = conversationPromptId
                EventBus.getDefault().post(inboxMarkConversationMediaPromptAsReadEvent)
            })
    }

    fun addConversationEncounterLikeUserId(conversation: InboxConversation?, inboxUserId: Int) {
        if (contextRef.get() == null || inboxUserId <= 0 || conversation == null) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        Completable.fromAction {
            conversation.addEncounterLikeUserId(inboxUserId)
            saveConversation(conversation)
            val event = InboxUpdateEncounterLikeEvent()
            event.conversationId = conversation.id
            EventBus.getDefault().post(event)
        }.subscribe()
    }

    @SuppressLint("CheckResult")
    fun deleteConversation(conversationId: Int) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        Completable.fromAction {
            val deletedConversation = getLocalConversation(conversationId)
            if (deletedConversation != null) {
                if (deletedConversation.messages.isNotEmpty()) {
                    val lastMessageId = deletedConversation.messages[deletedConversation.messages.size - 1].id
                    val request = DeleteMessages(appName, inboxUserId, mainDeviceId, languagePreference)
                    request.setLastDeletedMessageId(lastMessageId.toString())
                    retrofitConnector.inboxApi.deleteMessages(conversationId, request.queryMap)
                        .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
                        .`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
                        .subscribe(
                            { deleteLocalConversation(conversationId) },
                            { throwable: Throwable? ->
                                val inboxDeleteConversationEvent = InboxDeleteConversationEvent()
                                inboxDeleteConversationEvent.status = InboxDeleteConversationEvent.FAILED
                                inboxDeleteConversationEvent.conversationId = conversationId
                                inboxDeleteConversationEvent.throwable = throwable
                                EventBus.getDefault().post(inboxDeleteConversationEvent)
                            })
                } else {
                    deleteLocalConversation(conversationId)
                }
            }
        }.subscribe()
    }

    fun deleteLocalConversation(conversationId: Int) {
        Completable.fromAction {
            val conversation = getLocalConversation(conversationId)
            conversation?.let {
                if (it.isConversationRequest) {
                    numberOfConversationsRequest -= 1
                    if (InboxUtils.isUnreadConversation(it)) {
                        numberOfUnreadConversations -= 1
                        numberOfUnreadConversationsRequest -= 1
                    }
                    EventBus.getDefault().post(InboxUnreadMessagesCountChangedEvent())
                } else if (InboxUtils.isUnreadConversation(it)) {
                    numberOfUnreadConversations -= 1
                    EventBus.getDefault().post(InboxUnreadMessagesCountChangedEvent())
                }
                conversations.remove(it)
            }
            requestRemainingDirectConversationsCanCreate()
            roomManagement.deleteConversation(conversationId)
            val inboxDeleteConversationEvent = InboxDeleteConversationEvent()
            inboxDeleteConversationEvent.status = InboxDeleteConversationEvent.SUCCESS
            inboxDeleteConversationEvent.conversationId = conversationId
            inboxDeleteConversationEvent.conversation = conversation
            EventBus.getDefault().post(inboxDeleteConversationEvent)
        }.subscribeOn(Schedulers.io()).`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND)).subscribe()
    }

    @WorkerThread
    fun saveConversation(conversation: InboxConversation?) {
        conversation?.let {
            roomManagement.insertOrUpdateConversation(it)
        }
    }

    private fun requestUpdateUserProfile(conversationId: Int, isSystemMessage: Boolean) {
        Completable.fromAction {
            val inboxUserIds = getInboxUserIds(conversationId)
            if (inboxUserIds.isNotEmpty()) {
                inboxManagementCallback.requestUpdateUserProfile(conversationId, inboxUserIds, isSystemMessage)
            }
        }.subscribeOn(Schedulers.io()).`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND)).subscribe()
    }

    private fun requestUpdateUserProfile(listConversation: ArrayList<InboxConversation>) {
        if (listConversation.isNotEmpty()) {
            Completable.fromAction {
                val conversationIds = getConversationIds(listConversation)
                val inboxUserIds = getAllInboxUserIds(listConversation)
                if (!CollectionUtils.isEmpty(conversationIds) && !CollectionUtils.isEmpty(inboxUserIds)) {
                    inboxManagementCallback.requestUpdateUserProfile(conversationIds, inboxUserIds)
                }
            }.subscribeOn(Schedulers.io()).`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND)).subscribe()
        }
    }

    // Raw InboxConversation don't have users info -> requestUpdateUserProfile() from API Server
    // -> then app will callback updateUserProfile() to Inbox
    fun updateUserProfile(conversationId: Int, listProfile: ArrayList<InboxUser>, isSystemMessage: Boolean) {
        val indexedListProfile = HashMap<String, InboxUser>()
        synchronized(listProfile) {
            for (index in listProfile.indices) {
                val profile = listProfile[index]
                if (!indexedListProfile.containsKey(profile.inboxUserId.toString())) {
                    indexedListProfile[profile.inboxUserId.toString()] = profile
                }
            }
        }

        val updatingConversation = getLocalConversation(conversationId)
        if (updatingConversation != null) {
            val lstMessage = updatingConversation.messages
            for (index in lstMessage.indices) {
                val inboxMessage = lstMessage[index]
                inboxMessage.user?.let {
                    val profile = indexedListProfile[it.inboxUserId.toString()]
                    if (profile != null) {
                        inboxMessage.user = profile
                    }
                }
            }

            for (index in updatingConversation.users.indices) {
                val profile = updatingConversation.users[index]
                val indexedProfile = indexedListProfile[profile.inboxUserId.toString()]
                if (indexedProfile != null) {
                    profile.name = indexedProfile.name
                    profile.id = indexedProfile.id
                    profile.inboxUserId = indexedProfile.inboxUserId
                    profile.profilePhotoUrl = indexedProfile.profilePhotoUrl
                    profile.cacheTime = System.currentTimeMillis()
                    updatingConversation.users[index] = profile

                    if (updatingConversation.messages.isNotEmpty()) {
                        for (message in updatingConversation.messages) {
                            message.user?.let {
                                val indexedLocalProfile = indexedListProfile[it.inboxUserId.toString()]
                                if (indexedLocalProfile != null) {
                                    it.name = indexedLocalProfile.name
                                    it.id = indexedLocalProfile.id
                                    it.inboxUserId = indexedLocalProfile.inboxUserId
                                    it.profilePhotoUrl = indexedLocalProfile.profilePhotoUrl
                                }
                            }
                        }
                    }

                    for (l in updatingConversation.users.indices) {
                        val localProfile = updatingConversation.users[l]
                        val indexedLocalProfile = indexedListProfile[localProfile.inboxUserId.toString()]

                        if (indexedLocalProfile != null) {
                            localProfile.name = indexedLocalProfile.name
                            localProfile.id = indexedLocalProfile.id
                            localProfile.inboxUserId = indexedLocalProfile.inboxUserId
                            localProfile.profilePhotoUrl = indexedLocalProfile.profilePhotoUrl
                        }
                    }
                }
            }
            roomManagement.insertOrUpdateConversation(updatingConversation)
            if (conversationsNeedToNotify.contains(conversationId)) {
                val inboxMessageCreatedEvent = InboxMessageCreatedEvent()
                inboxMessageCreatedEvent.conversationId = conversationId
                inboxMessageCreatedEvent.isSystemMessage = isSystemMessage
                inboxMessageCreatedEvent.isConversationRequest = updatingConversation.isConversationRequest
                EventBus.getDefault().post(inboxMessageCreatedEvent)
                conversationsNeedToNotify.remove(conversationId)
            }

            val inboxUpdateProfileForConversationEvent = InboxUpdateProfileForConversationEvent()
            inboxUpdateProfileForConversationEvent.status = InboxUpdateProfileForConversationEvent.SUCCESS
            inboxUpdateProfileForConversationEvent.conversation = updatingConversation
            EventBus.getDefault().post(inboxUpdateProfileForConversationEvent)
        } else {
            val inboxUpdateProfileForConversationEvent = InboxUpdateProfileForConversationEvent()
            inboxUpdateProfileForConversationEvent.status = InboxUpdateProfileForConversationEvent.FAILED
            inboxUpdateProfileForConversationEvent.listInboxUser = listProfile
            EventBus.getDefault().post(inboxUpdateProfileForConversationEvent)
        }
    }

    // Raw InboxConversation don't have users info -> requestUpdateUserProfile() from API Server
    // -> then app will callback updateUserProfile() to Inbox
    fun updateUserProfile(listConversationId: ArrayList<Int>, listProfile: ArrayList<InboxUser>) {
        val indexedListProfile = HashMap<String, InboxUser>()
        synchronized(listProfile) {
            for (index in listProfile.indices) {
                val profile = listProfile[index]
                indexedListProfile[profile.inboxUserId.toString()] = profile
            }
        }

        synchronized(listConversationId) {
            for (i in listConversationId.indices) {
                val updatingConversation = getLocalConversation(listConversationId[i])
                if (updatingConversation != null) {
                    val lstMessage = updatingConversation.messages
                    for (index in lstMessage.indices) {
                        val inboxMessage = lstMessage[index]
                        inboxMessage.user?.inboxUserId?.toString()
                            ?.let { indexedListProfile[it] }
                            ?.also { inboxMessage.user = it }
                    }

                    for (j in updatingConversation.users.indices) {
                        val profile = updatingConversation.users[j]
                        val indexedProfile = indexedListProfile[profile.inboxUserId.toString()]
                        if (indexedProfile != null) {
                            profile.name = indexedProfile.name
                            profile.id = indexedProfile.id
                            profile.inboxUserId = indexedProfile.inboxUserId
                            profile.profilePhotoUrl = indexedProfile.profilePhotoUrl
                            profile.cacheTime = System.currentTimeMillis()

                            updatingConversation.users[j] = profile
                            if (updatingConversation.messages.isNotEmpty()) {
                                for (message in updatingConversation.messages) {
                                    message.user?.let {
                                        val indexedLocalProfile = indexedListProfile[it.inboxUserId.toString()]
                                        if (indexedLocalProfile != null) {
                                            it.name = indexedLocalProfile.name
                                            it.id = indexedLocalProfile.id
                                            it.inboxUserId = indexedLocalProfile.inboxUserId
                                            it.profilePhotoUrl = indexedLocalProfile.profilePhotoUrl
                                        }
                                    }
                                }
                            }

                            for (l in updatingConversation.users.indices) {
                                val localProfile = updatingConversation.users[l]
                                val indexedLocalProfile = indexedListProfile[localProfile.inboxUserId.toString()]

                                if (indexedLocalProfile != null) {
                                    localProfile.name = indexedLocalProfile.name
                                    localProfile.id = indexedLocalProfile.id
                                    localProfile.inboxUserId = indexedLocalProfile.inboxUserId
                                    localProfile.profilePhotoUrl = indexedLocalProfile.profilePhotoUrl
                                }
                            }
                        }
                    }
                    roomManagement.insertOrUpdateConversation(updatingConversation)
                }
            }
        }
        val inboxUpdateProfileForConversationsEvent = InboxUpdateProfileForConversationsEvent()
        inboxUpdateProfileForConversationsEvent.status = InboxUpdateProfileForConversationsEvent.SUCCESS
        EventBus.getDefault().post(inboxUpdateProfileForConversationsEvent)
    }

    private fun checkToSendMessageNotification(conversationId: Int, isConversationRequest: Boolean, isSystemMessage: Boolean) {
        if (!conversationsNeedToNotify.contains(conversationId)) {
            val inboxMessageCreatedEvent = InboxMessageCreatedEvent()
            inboxMessageCreatedEvent.conversationId = conversationId
            inboxMessageCreatedEvent.isConversationRequest = isConversationRequest
            inboxMessageCreatedEvent.isSystemMessage = isSystemMessage
            EventBus.getDefault().post(inboxMessageCreatedEvent)
        }
    }

    fun canSendMessage(senderInboxUserId: Int, receiverInboxUserId: Int) {
        val baseRequest = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        val params = baseRequest.queryMap
        params["receiver_id"] = receiverInboxUserId
        params["support_remind_user"] = true
        params["support_direct_chat"] = true
        val conversation = getLocalConversationByInboxUserId(receiverInboxUserId)
        if (conversation != null) {
            params["conversation_id"] = conversation.id
        }
        retrofitConnector.inboxApi.canSendMessage(senderInboxUserId, params)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe({ response: CanSendMessageResponse? ->
                val event = InboxCanSendMessageEvent()
                event.receiverInboxUserId = receiverInboxUserId
                event.response = response
                EventBus.getDefault().post(event)
            }, { throwable: Throwable? ->
                val event = InboxCanSendMessageEvent()
                event.receiverInboxUserId = receiverInboxUserId
                event.throwable = throwable
                EventBus.getDefault().post(event)
            })
    }

    private fun checkEnoughMessageToLoadMore(
        conversationId: Int, filterInboxMessages: List<InboxMessage?>,
        originMessages: List<InboxMessage?>, numberOfMessageNeedToLoad: Int
    ): Boolean {
        val totalNumber = filterInboxMessages.size + numberOfMessageNeedToLoad
        var lastMessageId: Long = 0
        //List messages return from api response (originMessages) have oldest id at the end of list because its not sort yet.
        originMessages[originMessages.size - 1]?.let { lastMessageId = it.id }
        if (totalNumber < MIN_MESSAGE_TO_LOADED) {
            getMoreMessages(conversationId, totalNumber, lastMessageId)
            return true
        }
        return false
    }

    @SuppressLint("CheckResult")
    fun getLatestMessages(conversationId: Int) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        val request = GetLatestMessages(appName, inboxUserId, mainDeviceId, languagePreference)
        request.setConversationId(conversationId.toString())
        retrofitConnector.inboxApi.getMessages(request.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe(
                { response: GetMessagesResponse ->
                    val inboxMessages = response.messages
                    val conversation = getLocalConversation(conversationId)
                    var needToContinueLoad = false
                    if (conversation != null && inboxMessages?.isNotEmpty() == true) {
                        conversation.messages.clear()
                        val filterInboxMessages: List<InboxMessage?> = filterFlashChatDoneMessages(inboxMessages)
                        val myUser = getMyInboxUser(conversation)
                        val friendUsers = getFriendInboxUsers(conversation)
                        for (message in filterInboxMessages) {
                            if (message != null) {
                                updateUsersForMessage(myUser, friendUsers, message)
                                insertMessage(conversation, message)
                            }
                        }
                        InboxUtils.sortMessagesById(conversation.messages)
                        conversation.seenTimestamp = response.seenTimestamp
                        updateSeenMessageIds(conversation)

                        roomManagement.saveInboxConversation(conversation)
                        needToContinueLoad = checkEnoughMessageToLoadMore(conversationId, filterInboxMessages, inboxMessages, 0)
                    }

                    val inboxGetLatestMessageEvent = InboxGetLatestMessageEvent()
                    inboxGetLatestMessageEvent.status = InboxGetLatestMessageEvent.SUCCESS
                    inboxGetLatestMessageEvent.isStopLoadMore = needToContinueLoad
                    inboxGetLatestMessageEvent.conversationId = conversationId
                    EventBus.getDefault().post(inboxGetLatestMessageEvent)
                },
                { throwable: Throwable? ->
                    val inboxGetLatestMessageEvent = InboxGetLatestMessageEvent()
                    inboxGetLatestMessageEvent.status = InboxGetLatestMessageEvent.FAILED
                    inboxGetLatestMessageEvent.conversationId = conversationId
                    inboxGetLatestMessageEvent.throwable = throwable
                    EventBus.getDefault().post(inboxGetLatestMessageEvent)
                })
    }

    @SuppressLint("CheckResult")
    fun getMoreMessages(conversationId: Int, numberOfMessageNeedToLoad: Int, messageId: Long) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        val conversation = getLocalConversation(conversationId)
        if (conversation == null) {
            val inboxGetMoreMessageEvent = InboxGetMoreMessageEvent()
            inboxGetMoreMessageEvent.status = InboxGetMoreMessageEvent.FAILED
            inboxGetMoreMessageEvent.conversationId = conversationId
            EventBus.getDefault().post(inboxGetMoreMessageEvent)
            return
        }

        var lastMessageId = messageId
        if (lastMessageId == 0L) {
            if (conversation.messages.isNotEmpty()) {
                for (i in conversation.messages.indices) {
                    if (conversation.messages[i].id != 0L) {
                        lastMessageId = conversation.messages[i].id
                        break
                    }
                }
            }
        }
        val request = GetMoreMessages(appName, inboxUserId, mainDeviceId, languagePreference)
        request.setConversationId(conversation.id.toString())
        request.setLastMessageId(lastMessageId.toString())
        retrofitConnector.inboxApi.getMessages(request.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe({ response: GetMessagesResponse ->
                val inboxMessages = response.messages
                val conversation1 = getLocalConversation(conversationId)
                var filterInboxMessages: ArrayList<InboxMessage> = ArrayList()
                var needToContinueLoad = false
                if (conversation1 != null && conversation1.id != 0 && inboxMessages?.isNotEmpty() == true) {
                    filterInboxMessages = filterFlashChatDoneMessages(inboxMessages)
                    val myUser = getMyInboxUser(conversation1)
                    val friendUsers = getFriendInboxUsers(conversation1)
                    for (message in filterInboxMessages) {
                        updateUsersForMessage(myUser, friendUsers, message)
                        insertMessage(conversation1, message)
                    }
                    InboxUtils.sortMessagesById(conversation1.messages)
                    conversation1.seenTimestamp = response.seenTimestamp
                    updateSeenMessageIds(conversation1)

                    roomManagement.saveInboxConversation(conversation1)
                    needToContinueLoad = checkEnoughMessageToLoadMore(
                        conversationId, filterInboxMessages, inboxMessages,
                        numberOfMessageNeedToLoad
                    )
                }
                val inboxGetMoreMessageEvent = InboxGetMoreMessageEvent()
                inboxGetMoreMessageEvent.status = InboxGetMoreMessageEvent.SUCCESS
                inboxGetMoreMessageEvent.messages = filterInboxMessages
                inboxGetMoreMessageEvent.isStopLoadMore = needToContinueLoad
                inboxGetMoreMessageEvent.conversationId = conversationId
                EventBus.getDefault().post(inboxGetMoreMessageEvent)
            }, { throwable: Throwable? ->
                val inboxGetMoreMessageEvent = InboxGetMoreMessageEvent()
                inboxGetMoreMessageEvent.status = InboxGetMoreMessageEvent.FAILED
                inboxGetMoreMessageEvent.conversationId = conversationId
                inboxGetMoreMessageEvent.throwable = throwable
                EventBus.getDefault().post(inboxGetMoreMessageEvent)
            })
    }

    private fun updateUsersForMessage(myInboxUser: InboxUser?, friendUsers: List<InboxUser>?, message: InboxMessage) {
        val currentUser = message.user ?: return
        val replacement = when {
            myInboxUser?.inboxUserId == currentUser.inboxUserId -> myInboxUser
            else -> friendUsers?.firstOrNull { it.inboxUserId == currentUser.inboxUserId }
        }
        if (replacement != null) {
            message.user = replacement
        }
    }

    private fun onSendMessageSuccess(conversation: InboxConversation, localInboxMessage: InboxMessage, responseMessage: InboxMessage) {
        localInboxMessage.conversationId = responseMessage.conversationId
        localInboxMessage.id = responseMessage.id
        localInboxMessage.createdAt = responseMessage.createdAt
        localInboxMessage.status = responseMessage.status

        if (conversation.resolveConversationRequestIfReplied(inboxUserId)) {
            numberOfConversationsRequest -= 1
            if (InboxUtils.isUnreadConversation(conversation)) {
                numberOfUnreadConversationsRequest -= 1
            }
            EventBus.getDefault().post(InboxConversationRequestResolved())
        }
        conversation.lastMessageCreatedAt = responseMessage.createdAt
        roomManagement.saveInboxConversation(conversation)
        InboxUtils.sortConversationsByTime(conversations)
        checkToSendMessageNotification(conversation.id, conversation.isConversationRequest, false)

        val inboxSendMessageEvent = InboxSendMessageEvent()
        inboxSendMessageEvent.status = InboxSendMessageEvent.SUCCESS
        inboxSendMessageEvent.conversationId = conversation.id
        EventBus.getDefault().post(inboxSendMessageEvent)
    }

    private fun onSendMessageFailure(conversation: InboxConversation, localInboxMessage: InboxMessage, throwable: Throwable?) {
        if (throwable is HttpException) {
            deleteLocalMessage(conversation, localInboxMessage.id)
        } else {
            localInboxMessage.status = InboxMessage.FAILED_MESSAGE
        }
        val inboxSendMessageEvent = InboxSendMessageEvent()
        inboxSendMessageEvent.status = InboxSendMessageEvent.FAILED
        inboxSendMessageEvent.conversationId = conversation.id
        inboxSendMessageEvent.throwable = throwable
        EventBus.getDefault().post(inboxSendMessageEvent)
    }

    private fun sendMediaMessage(conversation: InboxConversation?, localInboxMessage: InboxMessage?, sendMessageFlowable: Flowable<SendMessageResponse>?) {
        if (conversation != null && localInboxMessage != null && sendMessageFlowable != null
            && (localInboxMessage.photoPath?.isNotEmpty() == true || localInboxMessage.videoPath?.isNotEmpty() == true || localInboxMessage.audioPath?.isNotEmpty() == true)
        ) {
            sendMessageFlowable
                .subscribeOn(Schedulers.io())
                .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
                .subscribe({ response ->
                    val message = response.message
                    val preSignedPost = response.preSignedPost
                    if (message != null && !preSignedPost.isNullOrEmpty()) {
                        onSendMessageSuccess(conversation, localInboxMessage, message)
                        when {
                            message.messageType.equals(InboxMessage.MESSAGE_TYPE_PHOTO, ignoreCase = true) ->
                                uploadPhoto(conversation.id, localInboxMessage, preSignedPost.first(), isDirectMessage = false, isDirectChatByAd = false)

                            message.messageType.equals(InboxMessage.MESSAGE_TYPE_VIDEO, ignoreCase = true) ->
                                uploadVideo(conversation.id, localInboxMessage, preSignedPost, isDirectMessage = false, isDirectChatByAd = false)

                            message.messageType.equals(InboxMessage.MESSAGE_TYPE_AUDIO, ignoreCase = true) ->
                                uploadAudio(conversation.id, localInboxMessage, preSignedPost.first(), isDirectMessage = false, isDirectChatByAd = false)
                        }
                    } else {
                        onSendMessageFailure(conversation, localInboxMessage, null)
                    }
                }, { throwable: Throwable? -> onSendMessageFailure(conversation, localInboxMessage, throwable) })
        }
    }

    fun sendMessage(conversation: InboxConversation?, localInboxMessage: InboxMessage?) {
        // 1) basic guards
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        if (conversation == null || localInboxMessage == null) return

        // 2) common vals
        val conversationId = conversation.id
        val inboxUserIds = InboxUtils.getInboxUserIds(conversation)

        // 3) pull out paths and attachments into locals
        val photoPath = localInboxMessage.photoPath.takeIf { !it.isNullOrEmpty() }
        val attachedPhoto = localInboxMessage.attachedPhoto

        val videoPath = localInboxMessage.videoPath.takeIf { !it.isNullOrEmpty() }
        val attachedVideo = localInboxMessage.attachedVideo

        val audioPath = localInboxMessage.audioPath.takeIf { !it.isNullOrEmpty() }
        val attachedAudio = localInboxMessage.attachedAudio

        // 4) helper to build multipart map
        fun buildQueryMap(file: File, contentType: String) = mutableMapOf<String, Any>().apply {
            put(SendMessageBase.PARAM_FILE_TYPE, contentType)
            put(SendMessageBase.PARAM_FILE_SIZE, file.length())
        }

        // 5) pick branch
        when {
            // PHOTO
            photoPath != null && attachedPhoto != null -> {
                Single.fromCallable {
                    FileUtil.resizeImage(
                        contextRef.get(), photoPath,
                        Constants.MAX_PHOTO_WIDTH, Constants.MAX_PHOTO_HEIGHT
                    ).takeIf { it.isNotEmpty() } ?: photoPath
                }
                    .subscribeOn(Schedulers.computation())
                    .observeOn(AndroidSchedulers.mainThread())
                    .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
                    .subscribe({ resizedPath ->
                        localInboxMessage.photoPath = resizedPath
                        val photoFile = File(resizedPath)
                        val queryMap = buildQueryMap(photoFile, FileUtil.getMimeType(photoFile))
                        val request = SendMessagePhoto(
                            appName, inboxUserId, mainDeviceId, languagePreference,
                            conversationId, inboxUserIds, localInboxMessage.flashDuration, attachedPhoto
                        )
                        sendMediaMessage(conversation, localInboxMessage, retrofitConnector.inboxApi.sendMessagePhoto(request, queryMap))
                    }, { t ->
                        onSendMessageFailure(conversation, localInboxMessage, t)
                    })
            }

            // VIDEO
            videoPath != null && attachedVideo != null -> {
                val videoFile = File(videoPath)
                val queryMap = buildQueryMap(videoFile, FileUtil.getMimeType(videoFile))
                val request = SendMessageVideo(
                    appName, inboxUserId, mainDeviceId, languagePreference,
                    conversationId, inboxUserIds, localInboxMessage.flashDuration, attachedVideo
                )
                sendMediaMessage(conversation, localInboxMessage, retrofitConnector.inboxApi.sendMessageVideo(request, queryMap))
            }

            // AUDIO
            audioPath != null && attachedAudio != null -> {
                val audioFile = File(audioPath)
                val queryMap = buildQueryMap(audioFile, SendMessageBase.DEFAULT_AUDIO_EXTENSION)
                val request = SendMessageAudio(
                    appName, inboxUserId, mainDeviceId, languagePreference,
                    conversationId, inboxUserIds, localInboxMessage.flashDuration, attachedAudio
                )
                sendMediaMessage(conversation, localInboxMessage, retrofitConnector.inboxApi.sendMessageAudio(request, queryMap))
            }

            // TEXT or GIPHY
            else -> {
                val sendMessageFlowable: Flowable<SendMessageResponse>? =
                    localInboxMessage.giphyContent?.let { giphy ->
                        retrofitConnector.inboxApi.sendMessageGiphy(
                            SendMessageGiphy(
                                appName, inboxUserId, mainDeviceId, languagePreference,
                                conversationId, inboxUserIds, localInboxMessage.flashDuration, giphy
                            )
                        )
                    } ?: localInboxMessage.content?.let { content ->
                        retrofitConnector.inboxApi.sendMessageText(
                            SendMessageText(
                                appName, inboxUserId, mainDeviceId, languagePreference,
                                conversationId, inboxUserIds, localInboxMessage.flashDuration, content
                            )
                        )
                    }
                sendMessageFlowable
                    ?.subscribeOn(Schedulers.io())
                    ?.observeOn(Schedulers.computation())
                    ?.`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
                    ?.subscribe({ response ->
                        response.message?.let {
                            onSendMessageSuccess(conversation, localInboxMessage, response.message)
                            requestBroadcastMessage(conversation.id, localInboxMessage, isDirectMessage = false, isDirectChatByAd = false, numRetry = 0)
                        }
                    }, { throwable ->
                        onSendMessageFailure(conversation, localInboxMessage, throwable)
                    })
            }
        }
    }

    private fun onSendDirectMessageSuccess(userName: String, localInboxMessage: InboxMessage, responseMessage: InboxMessage, isDirectChatByAd: Boolean) {
        if (isDirectChatByAd && remainingDirectConversationsCanCreate > 0) {
            remainingDirectConversationsCanCreate -= 1
        }

        localInboxMessage.id = responseMessage.id
        localInboxMessage.conversationId = responseMessage.conversationId

        val conversationId = responseMessage.conversationId
        val conversation = getLocalConversation(conversationId)
        var inboxUser = getMyInboxUser(conversation)
        if (inboxUser == null) {
            inboxUser = InboxUser()
            inboxUser.name = userName
        }
        responseMessage.user = inboxUser

        if (conversation != null) {
            conversation.lastMessageCreatedAt = responseMessage.createdAt
            roomManagement.saveInboxConversation(conversation)
        }

        val inboxSendMessageEvent = InboxSendMessageEvent()
        inboxSendMessageEvent.isDirectMessage = true
        inboxSendMessageEvent.status = InboxSendMessageEvent.SUCCESS
        inboxSendMessageEvent.conversationId = conversationId
        EventBus.getDefault().post(inboxSendMessageEvent)
    }

    private fun onSendDirectMessageFailure(throwable: Throwable?) {
        val inboxSendMessageEvent = InboxSendMessageEvent()
        inboxSendMessageEvent.status = InboxSendMessageEvent.FAILED
        inboxSendMessageEvent.throwable = throwable
        inboxSendMessageEvent.isDirectMessage = true
        EventBus.getDefault().post(inboxSendMessageEvent)
    }

    private fun sendDirectMediaMessage(
        localInboxMessage: InboxMessage?,
        userName: String,
        isDirectChatByAd: Boolean,
        sendMessageFlowable: Flowable<SendMessageResponse>?
    ) {
        if (localInboxMessage != null && sendMessageFlowable != null && !TextUtils.isEmpty(userName)
            && (!TextUtils.isEmpty(localInboxMessage.photoPath) || !TextUtils.isEmpty(localInboxMessage.videoPath) || !TextUtils.isEmpty(localInboxMessage.audioPath))
        ) {
            sendMessageFlowable
                .subscribeOn(Schedulers.io())
                .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
                .subscribe({ response ->
                    val message = response.message
                    val preSignedPost = response.preSignedPost
                    if (message != null && !preSignedPost.isNullOrEmpty()) {
                        onSendDirectMessageSuccess(userName, localInboxMessage, message, isDirectChatByAd)
                        val conversationId = message.conversationId
                        when {
                            message.messageType.equals(InboxMessage.MESSAGE_TYPE_PHOTO, ignoreCase = true) ->
                                uploadPhoto(conversationId, localInboxMessage, preSignedPost.first(), true, isDirectChatByAd)

                            message.messageType.equals(InboxMessage.MESSAGE_TYPE_VIDEO, ignoreCase = true) ->
                                uploadVideo(conversationId, localInboxMessage, preSignedPost, true, isDirectChatByAd)

                            message.messageType.equals(InboxMessage.MESSAGE_TYPE_AUDIO, ignoreCase = true) ->
                                uploadAudio(conversationId, localInboxMessage, preSignedPost.first(), true, isDirectChatByAd)
                        }
                    } else {
                        onSendDirectMessageFailure(null)
                    }
                }, { throwable: Throwable? -> onSendDirectMessageFailure(throwable) })
        }
    }

    fun sendDirectMessage(localInboxMessage: InboxMessage, targetInboxUserId: Int, userName: String, isDirectChatByAd: Boolean) {
        val inboxUserIds = listOf(inboxUserId, targetInboxUserId)

        val photoPath = localInboxMessage.photoPath
        val videoPath = localInboxMessage.videoPath
        val audioPath = localInboxMessage.audioPath

        if (photoPath?.isNotEmpty() == true || videoPath?.isNotEmpty() == true || audioPath?.isNotEmpty() == true) {
            val queryMap: MutableMap<String, Any> = HashMap()
            //Photo message
            if (photoPath?.isNotEmpty() == true) {
                Single.fromCallable {
                    FileUtil.resizeImage(
                        contextRef.get(), photoPath,
                        Constants.MAX_PHOTO_WIDTH, Constants.MAX_PHOTO_HEIGHT
                    ).takeIf { it.isNotEmpty() } ?: photoPath
                }
                    .subscribeOn(Schedulers.computation())
                    .observeOn(AndroidSchedulers.mainThread())
                    .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
                    .subscribe({ resizedPath: String? ->
                        localInboxMessage.photoPath = resizedPath
                        val photoFile = File(Objects.requireNonNull(resizedPath))
                        queryMap[SendMessageBase.PARAM_FILE_TYPE] = FileUtil.getMimeType(photoFile)
                        queryMap[SendMessageBase.PARAM_FILE_SIZE] = photoFile.length()
                        val request = SendMessagePhoto(
                            appName, inboxUserId, mainDeviceId,
                            languagePreference, inboxUserIds, localInboxMessage.flashDuration, localInboxMessage.attachedPhoto!!
                        )
                        sendDirectMediaMessage(localInboxMessage, userName, isDirectChatByAd, retrofitConnector.inboxApi.sendMessagePhoto(request, queryMap))
                    }, { throwable: Throwable? -> this.onSendDirectMessageFailure(throwable) })
            } else if (videoPath?.isNotEmpty() == true) {
                val videoFile = File(videoPath)
                queryMap[SendMessageBase.PARAM_FILE_TYPE] = FileUtil.getMimeType(videoFile)
                queryMap[SendMessageBase.PARAM_FILE_SIZE] = videoFile.length()
                val request = SendMessageVideo(
                    appName, inboxUserId, mainDeviceId, languagePreference,
                    inboxUserIds, localInboxMessage.flashDuration, localInboxMessage.attachedVideo!!
                )
                sendDirectMediaMessage(localInboxMessage, userName, isDirectChatByAd, retrofitConnector.inboxApi.sendMessageVideo(request, queryMap))
            } else if (audioPath?.isNotEmpty() == true) {
                val audioFile = File(audioPath)
                queryMap[SendMessageBase.PARAM_FILE_TYPE] = SendMessageBase.DEFAULT_AUDIO_EXTENSION
                queryMap[SendMessageBase.PARAM_FILE_SIZE] = audioFile.length()
                val request = SendMessageAudio(
                    appName, inboxUserId, mainDeviceId, languagePreference,
                    inboxUserIds, localInboxMessage.flashDuration, localInboxMessage.attachedAudio!!
                )
                sendDirectMediaMessage(localInboxMessage, userName, isDirectChatByAd, retrofitConnector.inboxApi.sendMessageAudio(request, queryMap))
            }
        } else {
            val sendMessageFlowable: Flowable<SendMessageResponse>
            if (localInboxMessage.giphyContent != null) {
                val request = SendMessageGiphy(
                    appName, inboxUserId, mainDeviceId, languagePreference, inboxUserIds,
                    localInboxMessage.flashDuration, localInboxMessage.giphyContent!!
                )
                sendMessageFlowable = retrofitConnector.inboxApi.sendMessageGiphy(request)
            } else {
                val request = SendMessageText(
                    appName, inboxUserId, mainDeviceId, languagePreference, inboxUserIds,
                    localInboxMessage.flashDuration, localInboxMessage.content!!
                )
                sendMessageFlowable = retrofitConnector.inboxApi.sendMessageText(request)
            }
            sendMessageFlowable
                .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
                .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
                .subscribe({ response ->
                    val responseMessage = response.message
                    if (responseMessage != null) {
                        onSendDirectMessageSuccess(userName, localInboxMessage, responseMessage, isDirectChatByAd)
                        requestBroadcastMessage(
                            responseMessage.conversationId,
                            responseMessage, true, isDirectChatByAd, 0
                        )
                    } else {
                        onSendDirectMessageFailure(null)
                    }
                }, { throwable: Throwable? -> this.onSendDirectMessageFailure(throwable) })
        }
    }

    private fun uploadPhoto(
        conversationId: Int, localInboxMessage: InboxMessage,
        preSignedPost: S3PreSignedPost, isDirectMessage: Boolean, isDirectChatByAd: Boolean
    ) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }

        val photoPath = localInboxMessage.photoPath ?: return
        val s3Fields = preSignedPost.fields?.getLinkedStringMap() ?: return
        val s3UploadUrl = preSignedPost.url
        val requestFile = File(photoPath).asRequestBody(formDataContentType)
        val requestBodyMap = getRequestBodyLinkedMap(s3Fields).apply { put(SendMessageBase.PARAM_FILE, requestFile) }
        retrofitConnector.inboxS3Api.uploadMediaToS3(s3UploadUrl, requestBodyMap)
            .observeOn(Schedulers.io())
            .subscribeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
            .subscribe({
                requestBroadcastMessage(conversationId, localInboxMessage, isDirectMessage, isDirectChatByAd, 0)
                val inboxUploadPhotosEvent = InboxUploadPhotosEvent()
                inboxUploadPhotosEvent.status = InboxUploadPhotosEvent.SUCCESS
                inboxUploadPhotosEvent.conversationId = conversationId
                inboxUploadPhotosEvent.inboxMessage = localInboxMessage
                EventBus.getDefault().post(inboxUploadPhotosEvent)
            }, { throwable: Throwable? ->
                val inboxUploadPhotosEvent = InboxUploadPhotosEvent()
                inboxUploadPhotosEvent.status = InboxUploadPhotosEvent.FAILED
                inboxUploadPhotosEvent.conversationId = conversationId
                inboxUploadPhotosEvent.inboxMessage = localInboxMessage
                inboxUploadPhotosEvent.throwable = throwable
                EventBus.getDefault().post(inboxUploadPhotosEvent)
            })
    }

    private fun uploadVideo(
        conversationId: Int, localInboxMessage: InboxMessage,
        preSignedPosts: ArrayList<S3PreSignedPost>, isDirectMessage: Boolean, isDirectChatByAd: Boolean
    ) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }

        var videoS3PreSignedPost: S3PreSignedPost? = null
        var thumbnailS3PreSignedPost: S3PreSignedPost? = null
        for (preSignedPost in preSignedPosts) {
            preSignedPost.fields?.contentType?.let {
                if (it.contains(S3Util.CONTENT_TYPE_IMAGE)) {
                    thumbnailS3PreSignedPost = preSignedPost
                } else if (it.contains(S3Util.CONTENT_TYPE_VIDEO)) {
                    videoS3PreSignedPost = preSignedPost
                }
            }
        }
        // Upload video thumbnail
        thumbnailS3PreSignedPost?.let {
            it.fields?.getLinkedStringMap()?.let { s3Fields ->
                localInboxMessage.videoPath?.let { path ->
                    @Suppress("DEPRECATION")
                    ThumbnailUtils.createVideoThumbnail(path, MediaStore.Images.Thumbnails.MINI_KIND)
                }
                    ?.run {
                        ByteArrayOutputStream()
                            .apply { compress(Bitmap.CompressFormat.JPEG, 80, this) }.toByteArray().toRequestBody(imageContentType)
                    }?.let { thumbnailBody ->
                        getRequestBodyLinkedMap(s3Fields).apply { put(SendMessageBase.PARAM_FILE, thumbnailBody) }
                    }
            }?.also { bodyMap ->
                retrofitConnector.inboxS3Api
                    .uploadMediaToS3(it.url, bodyMap)
                    .observeOn(Schedulers.io())
                    .`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
                    .subscribe()
            }
        }
        // Upload video
        videoS3PreSignedPost?.let {
            it.fields?.getLinkedStringMap()?.let { s3Fields ->
                localInboxMessage.videoPath?.let { path -> File(path).asRequestBody(formDataContentType) }
                    ?.let { requestFile -> getRequestBodyLinkedMap(s3Fields).apply { put(SendMessageBase.PARAM_FILE, requestFile) } }
            }?.also { bodyMap ->
                retrofitConnector.inboxS3Api.uploadMediaToS3(it.url, bodyMap)
                    .observeOn(Schedulers.io())
                    .`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
                    .subscribe({
                        requestBroadcastMessage(conversationId, localInboxMessage, isDirectMessage, isDirectChatByAd, 0)
                        val inboxUploadVideoEvent = InboxUploadVideoEvent()
                        inboxUploadVideoEvent.status = InboxUploadVideoEvent.SUCCESS
                        inboxUploadVideoEvent.conversationId = conversationId
                        inboxUploadVideoEvent.inboxMessage = localInboxMessage
                        EventBus.getDefault().post(inboxUploadVideoEvent)
                    }, { throwable: Throwable? ->
                        val inboxUploadVideoEvent = InboxUploadVideoEvent()
                        inboxUploadVideoEvent.status = InboxUploadVideoEvent.FAILED
                        inboxUploadVideoEvent.conversationId = conversationId
                        inboxUploadVideoEvent.inboxMessage = localInboxMessage
                        inboxUploadVideoEvent.throwable = throwable
                        EventBus.getDefault().post(inboxUploadVideoEvent)
                    })
            }
        } ?: run {
            val inboxUploadVideoEvent = InboxUploadVideoEvent()
            inboxUploadVideoEvent.status = InboxUploadVideoEvent.FAILED
            inboxUploadVideoEvent.conversationId = conversationId
            inboxUploadVideoEvent.inboxMessage = localInboxMessage
            EventBus.getDefault().post(inboxUploadVideoEvent)
        }
    }

    private fun uploadAudio(
        conversationId: Int, localInboxMessage: InboxMessage,
        preSignedPost: S3PreSignedPost, isDirectMessage: Boolean, isDirectChatByAd: Boolean
    ) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }

        val audioPath = localInboxMessage.audioPath ?: return
        val s3Fields = preSignedPost.fields?.getLinkedStringMap() ?: return
        val s3UploadUrl = preSignedPost.url
        val requestFile = File(audioPath).asRequestBody(formDataContentType)
        val requestBodyMap = getRequestBodyLinkedMap(s3Fields).apply { put(SendMessageBase.PARAM_FILE, requestFile) }
        retrofitConnector.inboxS3Api.uploadMediaToS3(s3UploadUrl, requestBodyMap)
            .observeOn(Schedulers.io())
            .subscribeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
            .subscribe({
                requestBroadcastMessage(conversationId, localInboxMessage, isDirectMessage, isDirectChatByAd, 0)
                val inboxUploadAudioEvent = InboxUploadAudioEvent()
                inboxUploadAudioEvent.status = InboxUploadAudioEvent.SUCCESS
                inboxUploadAudioEvent.conversationId = conversationId
                inboxUploadAudioEvent.inboxMessage = localInboxMessage
                EventBus.getDefault().post(inboxUploadAudioEvent)
            }, { throwable: Throwable? ->
                val inboxUploadAudioEvent = InboxUploadAudioEvent()
                inboxUploadAudioEvent.status = InboxUploadAudioEvent.FAILED
                inboxUploadAudioEvent.conversationId = conversationId
                inboxUploadAudioEvent.inboxMessage = localInboxMessage
                inboxUploadAudioEvent.throwable = throwable
                EventBus.getDefault().post(inboxUploadAudioEvent)
            })
    }

    fun insertMessageReported(messageId: Long) {
        roomManagement.insertMessageReported(InboxMessageReported(messageId))
    }

    private fun isMessageReported(messageId: Long): Boolean {
        return roomManagement.isMessageReported(messageId)
    }

    private fun insertMessage(conversation: InboxConversation, newMessage: InboxMessage): Int {
        var insertPosition = -1

        newMessage.createdAt?.let {
            val latest = conversation.lastMessageCreatedAt
            if (latest == null || latest < it) {
                conversation.lastMessageCreatedAt = it
            }
        }
        if (isMessageReported(newMessage.id)) {
            newMessage.isReported = true
        }

        val messages = conversation.messages
        if (messages.isNotEmpty()) {
            for (i in messages.indices.reversed()) {
                if (messages[i].id == newMessage.id) {
                    newMessage.user?.let { messages[i].user = it }
                    break
                } else if (messages[i].id < newMessage.id) {
                    if (newMessage.user?.inboxUserId != inboxUserId) {
                        messages.add(i + 1, newMessage)
                        insertPosition = i + 1
                        break
                    } else if (newMessage.user?.inboxUserId == inboxUserId) {
                        if (messages[i].id == 0L) {
                            if (newMessage.content?.isNotEmpty() == true && newMessage.content.equals(messages[i].content, ignoreCase = true)) {
                                break
                            } else if (newMessage.attachedPhoto?.equals(messages[i].attachedPhoto) == true) {
                                break
                            } else if (newMessage.attachedVideo != null && messages[i].attachedVideo != null) {
                                if (newMessage.attachedVideo?.videoUrl?.isNotEmpty() == true
                                    && newMessage.attachedVideo?.videoUrl.equals(messages[i].attachedVideo?.videoUrl, ignoreCase = true)
                                ) {
                                    break
                                } else if (!TextUtils.isEmpty(newMessage.videoPath) && newMessage.videoPath.equals(messages[i].videoPath, ignoreCase = true)) {
                                    break
                                }
                            } else if (newMessage.attachedAudio != null && messages[i].attachedAudio != null) {
                                if (newMessage.attachedAudio?.audioUrl?.isNotEmpty() == true
                                    && newMessage.attachedAudio?.audioUrl.equals(messages[i].attachedAudio?.audioUrl, ignoreCase = true)
                                ) {
                                    break
                                } else if (!TextUtils.isEmpty(newMessage.audioPath)
                                    && newMessage.audioPath.equals(messages[i].audioPath, ignoreCase = true)
                                ) {
                                    break
                                }
                            }
                        } else {
                            messages.add(i + 1, newMessage)
                            insertPosition = i + 1
                            break
                        }
                    } else {
                        messages.add(i + 1, newMessage)
                        insertPosition = i + 1
                        break
                    }
                } else {
                    if (i > 0 && messages[i - 1].id < newMessage.id) {
                        messages.add(i, newMessage)
                        insertPosition = i
                        break
                    } else if (i == 0) {
                        messages.add(0, newMessage)
                        insertPosition = 0
                        break
                    }
                }
            }
        } else {
            messages.add(newMessage)
            insertPosition = 0
        }
        return insertPosition
    }

    private fun requestBroadcastMessage(
        conversationId: Int, inboxMessage: InboxMessage?,
        isDirectMessage: Boolean, isDirectChatByAd: Boolean, numRetry: Int
    ) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        if (inboxMessage == null) {
            return
        }
        val request = BroadcastMessage(appName, inboxUserId, mainDeviceId, languagePreference)
        request.sentDirectChatByAd = isDirectChatByAd
        retrofitConnector.inboxApi.requestBroadcastNewMessage(inboxMessage.id, request)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
            .subscribe({
                if (isDirectMessage && !conversationsNeedToNotify.contains(conversationId)) {
                    conversationsNeedToNotify.add(conversationId)
                    refreshConversation(inboxMessage.conversationId, false, 0)
                }
            }, {
                if (numRetry < MAX_NUM_RETRY) {
                    requestBroadcastMessage(
                        conversationId, inboxMessage,
                        isDirectMessage, isDirectChatByAd, numRetry + 1
                    )
                }
            })
    }

    fun deleteMessage(conversationId: Int, message: InboxMessage?) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        if (conversationId <= 0 || message == null) {
            return
        }
        val base = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        retrofitConnector.inboxApi.deleteMessage(message.id, base.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
            .subscribe({
                val conversation = getLocalConversation(conversationId)
                deleteLocalMessage(conversation, message.id)

                val inboxDeleteMessageEvent = InboxDeleteMessageEvent()
                inboxDeleteMessageEvent.status = InboxDeleteMessageEvent.SUCCESS
                inboxDeleteMessageEvent.conversationId = conversationId
                inboxDeleteMessageEvent.inboxMessage = message
                EventBus.getDefault().post(inboxDeleteMessageEvent)
            }, {
                val inboxDeleteMessageEvent = InboxDeleteMessageEvent()
                inboxDeleteMessageEvent.status = InboxDeleteMessageEvent.FAILED
                inboxDeleteMessageEvent.conversationId = conversationId
                inboxDeleteMessageEvent.inboxMessage = message
                EventBus.getDefault().post(inboxDeleteMessageEvent)
            })
    }

    private fun deleteLocalMessage(conversation: InboxConversation?, messageId: Long) {
        if (conversation != null) {
            synchronized(conversation) {
                for (i in conversation.messages.indices) {
                    if (conversation.messages[i].id == messageId) {
                        conversation.messages.removeAt(i)
                        roomManagement.saveInboxConversation(conversation)
                        val inboxMessageDeletedEvent = InboxMessageDeletedEvent()
                        inboxMessageDeletedEvent.conversationId = conversation.id
                        inboxMessageDeletedEvent.messageId = messageId
                        EventBus.getDefault().post(inboxMessageDeletedEvent)
                        break
                    }
                }
            }
        }
    }

    fun sendReadMessage(inboxConversation: InboxConversation) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        if (!InboxUtils.isUnreadConversation(inboxConversation)) {
            return
        }
        Completable.fromAction {
            inboxConversation.status = ConversationStatus.READ
            val request = UpdateConversationStatus(appName, inboxUserId, mainDeviceId, languagePreference)
            request.status = ConversationStatus.READ
            retrofitConnector.inboxApi
                .updateConversationStatus(request, inboxConversation.id, request.queryMap)
                .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
                .`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
                .subscribe({
                    inboxConversation.status = ConversationStatus.READ
                    inboxConversation.newMessagesCount = 0
                    roomManagement.saveInboxConversation(inboxConversation)

                    numberOfUnreadConversations -= 1
                    if (inboxConversation.isConversationRequest) {
                        numberOfUnreadConversationsRequest -= 1
                    }
                    EventBus.getDefault().post(InboxUnreadMessagesCountChangedEvent())

                    val inboxUpdateConversationStatusEvent = InboxUpdateConversationStatusEvent()
                    inboxUpdateConversationStatusEvent.status = InboxUpdateConversationStatusEvent.SUCCESS
                    inboxUpdateConversationStatusEvent.conversationId = inboxConversation.id
                    EventBus.getDefault().post(inboxUpdateConversationStatusEvent)
                }, { throwable: Throwable? ->
                    val inboxUpdateConversationStatusEvent = InboxUpdateConversationStatusEvent()
                    inboxUpdateConversationStatusEvent.status = InboxUpdateConversationStatusEvent.FAILED
                    inboxUpdateConversationStatusEvent.conversationId = inboxConversation.id
                    inboxUpdateConversationStatusEvent.throwable = throwable
                    EventBus.getDefault().post(inboxUpdateConversationStatusEvent)
                })
        }.subscribe()
    }

    fun sendReadFlashMessage(inboxMessage: InboxMessage?) {
        if (contextRef.get() == null || inboxUserId <= 0 || inboxMessage == null) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }
        val baseRequest = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        retrofitConnector.inboxApi.sendReadFlashMessage(inboxMessage.id, baseRequest.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
            .subscribe({
                val inboxSendReadFlashMessageEvent = InboxSendReadFlashMessageEvent()
                inboxSendReadFlashMessageEvent.status = InboxSendReadFlashMessageEvent.SUCCESS
                inboxSendReadFlashMessageEvent.inboxMessage = inboxMessage
                EventBus.getDefault().post(inboxSendReadFlashMessageEvent)
            }, {
                val inboxSendReadFlashMessageEvent = InboxSendReadFlashMessageEvent()
                inboxSendReadFlashMessageEvent.status = InboxSendReadFlashMessageEvent.FAILED
                inboxSendReadFlashMessageEvent.inboxMessage = inboxMessage
                EventBus.getDefault().post(inboxSendReadFlashMessageEvent)
            })
    }

    fun reportMessage(messageId: Long, category: String?, layer1Reason: String?, layer2Reason: String?, layer3Reason: String?): Completable? {
        if (contextRef.get() == null || inboxUserId <= 0 || messageId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return null
        }
        val reportMessage = ReportMessage(appName, inboxUserId, mainDeviceId, languagePreference)
        reportMessage.category = category
        reportMessage.layer1Reason = layer1Reason
        reportMessage.layer2Reason = layer2Reason
        reportMessage.layer3Reason = layer3Reason
        return retrofitConnector.inboxApi.reportMessage(messageId, reportMessage)
    }

    fun blockUser(friendUserId: Int, friendUserInboxId: Int) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            val event = InboxBlockUserEvent()
            event.status = InboxBlockUserEvent.NULL
            EventBus.getDefault().post(event)
            return
        }
        val baseRequest = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        retrofitConnector.inboxApi.blockUser(friendUserInboxId, baseRequest.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe({ conversation: InboxConversation ->
                deleteLocalConversation(conversation.id)
                val event = InboxBlockUserEvent()
                event.status = InboxBlockUserEvent.SUCCESS
                event.feedUserId = friendUserId
                event.inboxUserId = friendUserInboxId
                EventBus.getDefault().post(event)
            }, {
                val inboxUnblockUserEvent = InboxUnblockUserEvent()
                inboxUnblockUserEvent.status = InboxUnblockUserEvent.FAILED
                inboxUnblockUserEvent.feedUserId = friendUserId
                inboxUnblockUserEvent.inboxUserId = friendUserInboxId
                EventBus.getDefault().post(inboxUnblockUserEvent)
            })
    }

    fun unblockUser(friendUserId: Int, friendUserInboxId: Int) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            val event = InboxUnblockUserEvent()
            event.status = InboxBlockUserEvent.NULL
            EventBus.getDefault().post(event)
            return
        }
        val baseRequest = BaseRequest(appName, inboxUserId, mainDeviceId, languagePreference)
        retrofitConnector.inboxApi.unblockUser(friendUserInboxId, baseRequest.queryMap)
            .subscribeOn(Schedulers.io()).observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable(ScopeProvider.UNBOUND))
            .subscribe({ conversation: InboxConversation ->
                if (conversation.id != 0) {
                    if (!conversations.contains(conversation)) {
                        conversations.add(conversation)
                        InboxUtils.sortMessagesById(conversation.messages)
                    }
                    requestUpdateUserProfile(conversation.id, false)
                    roomManagement.saveInboxConversation(conversation)
                    InboxUtils.sortConversationsByTime(conversations)
                    requestRemainingDirectConversationsCanCreate()
                }
                val inboxUnblockUserEvent = InboxUnblockUserEvent()
                inboxUnblockUserEvent.status = InboxUnblockUserEvent.SUCCESS
                inboxUnblockUserEvent.feedUserId = friendUserId
                inboxUnblockUserEvent.inboxUserId = friendUserInboxId
                EventBus.getDefault().post(inboxUnblockUserEvent)
            }, {
                val inboxUnblockUserEvent = InboxUnblockUserEvent()
                inboxUnblockUserEvent.status = InboxUnblockUserEvent.FAILED
                inboxUnblockUserEvent.feedUserId = friendUserId
                inboxUnblockUserEvent.inboxUserId = friendUserInboxId
                EventBus.getDefault().post(inboxUnblockUserEvent)
            })
    }

    fun checkToUpdateConversationLikeStatus(friendUserInboxId: Int, isLiked: Boolean) {
        Completable.fromAction {
            val localConversation = getLocalConversationByInboxUserId(friendUserInboxId)
            if (localConversation != null && localConversation.isSourceEncounter) {
                if (isLiked) {
                    localConversation.addEncounterLikeUserId(inboxUserId)
                } else {
                    localConversation.removeEncounterLikeUserId(inboxUserId)
                }
            }
        }.subscribeOn(Schedulers.io()).subscribe()
    }

    fun solvePusherMessageCreated(data: String?) {
        val message = InboxUtils.getInboxMessage(data)
        if (message != null) {
            Completable.fromAction {
                val inboxConversation = getLocalConversation(message.conversationId)
                message.user?.inboxUserId?.let { userId ->
                    inboxConversation?.users.orEmpty().firstOrNull { it.inboxUserId == userId }?.also { message.user = it }
                }
                if (message.user != null) {
                    processForNormalMessage(message, inboxConversation)
                } else {
                    processForSystemMessage(message, inboxConversation)
                }
            }.subscribeOn(Schedulers.io())
                .observeOn(Schedulers.computation())
                .`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
                .subscribe({}, { processForSystemMessage(message, null) })
        }
    }

    fun solvePusherMessageDeleted(data: String) {
        val jsonObject = JsonParser.parseString(data).asJsonObject
        if (jsonObject != null) {
            val conversationId = jsonObject["conversation_id"].asInt
            val messageId = jsonObject["id"].asLong
            Completable.fromAction {
                val conversation = getLocalConversation(conversationId)
                if (conversation != null) {
                    deleteLocalMessage(conversation, messageId)
                }
            }.subscribeOn(Schedulers.io()).subscribe()
        }
    }

    fun solvePusherConversationSeen(data: String) {
        Completable.fromAction {
            val jsonObject = JsonParser.parseString(data).asJsonObject
            if (jsonObject != null) {
                val conversationId = jsonObject["conversation_id"].asInt
                val inboxUserId = jsonObject["jsh_inbox_user_id"].asInt
                val seenAt = jsonObject["seen_at"].asString

                val conversation = getLocalConversation(conversationId)
                if (conversation != null) {
                    var seenTimeStamps = conversation.seenTimestamp
                    if (seenTimeStamps == null) {
                        seenTimeStamps = ArrayList()
                        val inboxSeenTime = InboxSeenTime()
                        inboxSeenTime.inboxUserId = inboxUserId
                        inboxSeenTime.seenAt = seenAt
                        seenTimeStamps.add(inboxSeenTime)
                    } else {
                        for (i in seenTimeStamps.indices) {
                            if (seenTimeStamps[i].inboxUserId == inboxUserId) {
                                seenTimeStamps[i].seenAt = seenAt
                                break
                            }
                        }
                    }
                    conversation.seenTimestamp = seenTimeStamps
                    updateSeenMessageIds(conversation)

                    val inboxConversationSeenEvent = InboxConversationSeenEvent()
                    inboxConversationSeenEvent.conversationId = conversationId
                    inboxConversationSeenEvent.feedUserId = inboxUserId
                    inboxConversationSeenEvent.seenAt = seenAt
                    EventBus.getDefault().post(inboxConversationSeenEvent)
                }
            }
        }.subscribeOn(Schedulers.io()).subscribe()
    }

    fun solvePusherRewindEncounterLike(data: String) {
        Completable.fromAction {
            val jsonObject = JsonParser.parseString(data).asJsonObject
            if (jsonObject != null) {
                val conversationId = jsonObject["conversation_id"].asInt
                val conversation = getLocalConversation(conversationId)
                if (conversation != null) {
                    val jsonArray = jsonObject["encounter_like_by_user_ids"].asJsonArray
                    val encounterLikeByUserIds = IntArray(jsonArray.size())
                    // Loop through the JSON array and populate the int array
                    for (i in 0..<jsonArray.size()) {
                        encounterLikeByUserIds[i] = jsonArray[i].asInt
                    }
                    conversation.encounterLikeByUserIds = encounterLikeByUserIds
                    saveConversation(conversation)

                    val event = InboxRewindEncounterLikeEvent()
                    event.conversationId = conversationId
                    EventBus.getDefault().post(event)
                }
            }
        }.subscribeOn(Schedulers.io()).subscribe()
    }

    fun solvePusherLikeReceived(friendInboxUserId: Int) {
        Completable.fromAction {
            val conversation = getLocalConversationByInboxUserId(friendInboxUserId)
            if (conversation != null && conversation.isSourceEncounter && !conversation.isEncounterLiked(friendInboxUserId)) {
                conversation.addEncounterLikeUserId(friendInboxUserId)
                roomManagement.saveInboxConversation(conversation)
                val event = InboxUpdateEncounterLikeEvent()
                event.conversationId = conversation.id
                EventBus.getDefault().post(event)
            }
        }.subscribeOn(Schedulers.io()).subscribe()
    }

    fun solvePusherAskingMediaPermissionCreated(data: String) {
        Completable.fromAction {
            val jsonObject = JsonParser.parseString(data).asJsonObject
            if (jsonObject != null) {
                val targetInboxUserId = jsonObject.optInt("user_id") ?: -1
                val targetConversationId = jsonObject.optInt("conversation_id") ?: -1
                if (targetInboxUserId == inboxUserId) {
                    getLocalConversation(targetConversationId)?.let { conversation ->
                        val promptId = jsonObject.optInt("id") ?: -1
                        // Only add if we don’t already have one with the same ID
                        if (conversation.conversationPrompts.none { it.id == promptId }) {
                            val conversationPrompt = InboxConversationPrompt().apply {
                                id = promptId
                                conversationId = targetConversationId
                                inboxUserId = targetInboxUserId
                                promptType = jsonObject.optString("prompt_type")
                                status = jsonObject.optString("status")
                                readAt = jsonObject.optString("read_at")
                                createdAt = jsonObject.optString("created_at")
                                closed = jsonObject.optBoolean("closed") == true
                            }
                            conversation.conversationPrompts.add(conversationPrompt)
                            saveConversation(conversation)

                            val inboxConversationPromptCreatedEvent = InboxConversationPromptCreatedEvent()
                            inboxConversationPromptCreatedEvent.conversationId = conversation.id
                            inboxConversationPromptCreatedEvent.conversationPromptId = conversationPrompt.id
                            inboxConversationPromptCreatedEvent.isConversationRequest = conversation.isConversationRequest
                            EventBus.getDefault().post(inboxConversationPromptCreatedEvent)

                            val inboxConversationMediaChangedEvent = InboxConversationMediaChangedEvent()
                            inboxConversationMediaChangedEvent.status = InboxBaseEvent.SUCCESS
                            inboxConversationMediaChangedEvent.conversationId = conversation.id
                            inboxConversationMediaChangedEvent.conversationPromptId = conversationPrompt.id
                            EventBus.getDefault().post(inboxConversationMediaChangedEvent)
                        }
                    }
                }
            }
        }.subscribeOn(Schedulers.io()).subscribe()
    }

    fun solvePusherAskingMediaPermissionReplied(data: String) {
        Completable.fromAction {
            val jsonObject = JsonParser.parseString(data).asJsonObject
            if (jsonObject != null) {
                val targetInboxUserId = jsonObject.optInt("user_id") ?: -1
                val targetConversationId = jsonObject.optInt("conversation_id") ?: -1
                if (targetInboxUserId == inboxUserId) {
                    getLocalConversation(targetConversationId)?.let { conversation ->
                        val promptId = jsonObject.optInt("id") ?: -1
                        // Only add if we don’t already have one with the same ID
                        if (conversation.conversationPrompts.none { it.id == promptId }) {
                            val conversationPrompt = InboxConversationPrompt().apply {
                                id = promptId
                                conversationId = targetConversationId
                                inboxUserId = targetInboxUserId
                                promptType = jsonObject.optString("prompt_type")
                                status = jsonObject.optString("status")
                                readAt = jsonObject.optString("read_at")
                                createdAt = jsonObject.optString("created_at")
                                closed = jsonObject.optBoolean("closed") == true
                            }
                            conversation.conversationPrompts.add(conversationPrompt)
                            InboxUtils.getFriendInboxUser(conversation, inboxUserId)?.let {
                                if (InboxConversationPrompt.TYPE_ACCEPTED == conversationPrompt.promptType) {
                                    conversation.setReceivingMediaEnabled(it.inboxUserId, true)
                                } else if (InboxConversationPrompt.TYPE_REJECTED == conversationPrompt.promptType) {
                                    conversation.setReceivingMediaEnabled(it.inboxUserId, false)
                                }
                            }
                            saveConversation(conversation)

                            val inboxConversationPromptCreatedEvent = InboxConversationPromptCreatedEvent()
                            inboxConversationPromptCreatedEvent.conversationId = conversation.id
                            inboxConversationPromptCreatedEvent.conversationPromptId = conversationPrompt.id
                            inboxConversationPromptCreatedEvent.isConversationRequest = conversation.isConversationRequest
                            EventBus.getDefault().post(inboxConversationPromptCreatedEvent)

                            val inboxConversationMediaChangedEvent = InboxConversationMediaChangedEvent()
                            inboxConversationMediaChangedEvent.status = InboxBaseEvent.SUCCESS
                            inboxConversationMediaChangedEvent.conversationId = conversation.id
                            inboxConversationMediaChangedEvent.conversationPromptId = conversationPrompt.id
                            EventBus.getDefault().post(inboxConversationMediaChangedEvent)
                        }
                    }
                }
            }
        }.subscribeOn(Schedulers.io()).subscribe()
    }

    private fun processForNormalMessage(messageInfo: InboxMessage, conversation: InboxConversation?) {
        if (contextRef.get() == null || inboxUserId <= 0) {
            EventBus.getDefault().post(InboxNullEvent())
            return
        }

        conversation?.let {
            messageInfo.user?.let {
                val isFriendMessage = it.inboxUserId != inboxUserId
                if (isFriendMessage) {
                    var seenTimeStamps = conversation.seenTimestamp
                    if (seenTimeStamps == null) {
                        seenTimeStamps = ArrayList()
                        val inboxSeenTime = InboxSeenTime()
                        inboxSeenTime.inboxUserId = it.inboxUserId
                        inboxSeenTime.seenAt = messageInfo.createdAt
                        seenTimeStamps.add(inboxSeenTime)
                    } else {
                        for (i in seenTimeStamps.indices) {
                            if (seenTimeStamps[i].inboxUserId == it.inboxUserId) {
                                seenTimeStamps[i].seenAt = messageInfo.createdAt
                                break
                            }
                        }
                    }
                    conversation.seenTimestamp = seenTimeStamps
                    updateSeenMessageIds(conversation)
                    conversation.lastMessageCreatedAt = messageInfo.createdAt
                }
                val insertPosition = insertMessage(conversation, messageInfo)
                if (insertPosition != -1) {
                    conversation.newMessagesCount += 1
                    if (isFriendMessage && !InboxUtils.isUnreadConversation(conversation)) {
                        conversation.status = ConversationStatus.UNREAD
                        numberOfUnreadConversations += 1
                        if (conversation.isConversationRequest) {
                            numberOfUnreadConversationsRequest += 1
                        }
                        EventBus.getDefault().post(InboxUnreadMessagesCountChangedEvent())
                    }
                }
                if (conversation.resolveConversationRequestIfReplied(inboxUserId)) {
                    numberOfConversationsRequest -= 1
                    if (InboxUtils.isUnreadConversation(conversation)) {
                        numberOfUnreadConversationsRequest -= 1
                    }
                    EventBus.getDefault().post(InboxConversationRequestResolved())
                }
                roomManagement.saveInboxConversation(conversation)
                InboxUtils.sortConversationsByTime(conversations)
                checkToSendMessageNotification(messageInfo.conversationId, conversation.isConversationRequest, false)
            }
        } ?: run {
            val conversationId = messageInfo.conversationId
            if (!conversationsNeedToNotify.contains(conversationId)) {
                conversationsNeedToNotify.add(conversationId)
                refreshConversation(messageInfo.conversationId, false, 0)
            }
        }
    }

    private fun processForSystemMessage(messageInfo: InboxMessage, conversation: InboxConversation?) {
        conversation?.let {
            messageInfo.content?.let {
                val indexOfNameConversation = it.lastIndexOf(" named the conversation: ")
                if (indexOfNameConversation > 0) {
                    var newConversationName = it.substring(indexOfNameConversation)
                    newConversationName = newConversationName.substring(newConversationName.indexOf('"') + 1, newConversationName.length - 1)
                    conversation.name = newConversationName
                }
            }
            conversation.lastMessageCreatedAt = messageInfo.createdAt
            val insertPosition = insertMessage(conversation, messageInfo)
            // if insertPosition <= 0, this is a first message (system message).
            if (insertPosition > 0) {
                conversation.newMessagesCount += 1
            }
            // Check to resolve conversation request
            if (conversation.isConversationRequest
                && (Constants.YOU_TWO_HAVE_BEEN_MATCHED.equals(messageInfo.content, ignoreCase = true)
                        || Constants.YOU_HAVE_FOUND_YOU_SOUL_MATCH.equals(messageInfo.content, ignoreCase = true))
            ) {
                conversation.isConversationRequest = false
                numberOfConversationsRequest -= 1
                if (InboxUtils.isUnreadConversation(conversation)) {
                    numberOfUnreadConversationsRequest -= 1
                }
                EventBus.getDefault().post(InboxConversationRequestResolved())
            }
            // Check to update conversation status and unread count
            if (!ConversationStatus.LEFT.equals(conversation.status, ignoreCase = true) && !InboxUtils.isUnreadConversation(conversation)) {
                conversation.status = ConversationStatus.UNREAD
                // update unread message
                numberOfUnreadConversations += 1
                if (conversation.isConversationRequest) {
                    numberOfUnreadConversationsRequest += 1
                }
                EventBus.getDefault().post(InboxUnreadMessagesCountChangedEvent())
            }
            roomManagement.saveInboxConversation(conversation)
            InboxUtils.sortConversationsByTime(conversations)
            checkToSendMessageNotification(messageInfo.conversationId, conversation.isConversationRequest, true)
        } ?: run {
            val conversationId = messageInfo.conversationId
            if (!conversationsNeedToNotify.contains(conversationId)) {
                conversationsNeedToNotify.add(conversationId)
                refreshConversation(messageInfo.conversationId, true, 0)
            }
        }
    }

    fun updateSeenMessageIds(conversation: InboxConversation) {
        conversation.seenTimestamp?.let {
            if (it.isNotEmpty()) {
                val friendInboxUsers = getFriendInboxUsers(conversation)
                for (seenTime in it) {
                    for (inboxUser in friendInboxUsers) {
                        if (seenTime.inboxUserId == inboxUser.inboxUserId) {
                            val seenAt = conversation.getSeenAt(inboxUser)
                            for (i in conversation.messages.indices.reversed()) {
                                val inboxMessage = conversation.messages[i]
                                if (inboxMessage.user?.inboxUserId == inboxUserId && inboxMessage.flashDuration == 0
                                    && DateTimeUtil.getTime(inboxMessage.createdAt, DateTimeUtil.defaultFormatExtended) > 0
                                    && (DateTimeUtil.getTime(inboxMessage.createdAt, DateTimeUtil.defaultFormatExtended)
                                            <= DateTimeUtil.getTime(seenAt, DateTimeUtil.defaultFormatExtended))
                                ) {
                                    seenTime.seenMessageId = inboxMessage.id
                                    break
                                }
                            }
                            break
                        }
                    }
                }
            }
        }
    }

    private fun filterFlashChatDoneMessages(inboxMessages: List<InboxMessage>): ArrayList<InboxMessage> =
        inboxMessages.filterTo(ArrayList()) { message ->
            // if not a “flash” or no user, keep it
            val userId = message.user?.inboxUserId
            if (message.flashDuration <= 0 || userId == null) return@filterTo true
            // otherwise only keep if *no* other user has seen it
            message.seenByInboxUserIds.orEmpty().none { seenUserId -> seenUserId != userId }
        }

    companion object {
        private const val MAX_NUM_RETRY = 3
        private const val MIN_MESSAGE_TO_LOADED = 15

        private var INSTANCE: InboxManagement? = null

        fun getInstance(
            context: Context, isStaging: Boolean, stagingAuthorizationUser: String?, stagingAuthorizationPassword: String?,
            inboxServerAddress: String, inboxServerToken: String, inboxServerSalt: String, callback: InboxManagementCallback
        ): InboxManagement =
            INSTANCE ?: synchronized(this) {
                INSTANCE ?: InboxManagement(
                    context.applicationContext, isStaging, stagingAuthorizationUser,
                    stagingAuthorizationPassword, inboxServerAddress, inboxServerToken, inboxServerSalt, callback
                )
                    .also { INSTANCE = it }
            }
    }
}
