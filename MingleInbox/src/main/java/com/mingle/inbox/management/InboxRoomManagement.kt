package com.mingle.inbox.management

import android.annotation.SuppressLint
import android.content.Context
import androidx.annotation.WorkerThread
import com.mingle.global.utils.Log
import com.mingle.inbox.model.InboxConversation
import com.mingle.inbox.model.InboxMessageReported
import com.mingle.inbox.room.InboxDatabase
import com.uber.autodispose.AutoDispose
import com.uber.autodispose.ScopeProvider
import io.reactivex.Completable
import io.reactivex.Single
import io.reactivex.schedulers.Schedulers
import java.lang.ref.WeakReference

class InboxRoomManagement(context: Context) {
    private val mContextReference = WeakReference(context)

    val conversationsFromDatabase: Single<List<InboxConversation>>
        get() = mContextReference.get()?.let { InboxDatabase.getInstance(it).dao().queryList() } ?: Single.just(emptyList())

    @WorkerThread
    fun getLocalConversation(conversationId: Int): InboxConversation? {
        return mContextReference.get()?.let { InboxDatabase.getInstance(it).dao().getLocalConversationById(conversationId) }
    }

    @WorkerThread
    fun deleteConversation(conversationId: Int) {
        mContextReference.get()?.let { InboxDatabase.getInstance(it).dao().delete(conversationId) }
    }

    @WorkerThread
    fun insertOrUpdateConversation(conversation: InboxConversation) {
        mContextReference.get()?.let { InboxDatabase.getInstance(it).dao().insert(conversation) }
    }

    @SuppressLint("CheckResult")
    fun saveInboxConversation(inboxConversation: InboxConversation) {
        Completable.fromAction { mContextReference.get()?.let { InboxDatabase.getInstance(it).dao().insert(inboxConversation) } }
            .subscribeOn(Schedulers.io()).`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
            .subscribe({}, { })
    }

    @SuppressLint("CheckResult")
    fun saveInboxConversations(inboxConversations: List<InboxConversation>) {
        Completable.fromAction { mContextReference.get()?.let { InboxDatabase.getInstance(it).dao().insert(inboxConversations) } }
            .subscribeOn(Schedulers.io()).`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
            .subscribe({}, { })
    }

    @WorkerThread
    fun insertMessageReported(messageReported: InboxMessageReported) {
        Completable.fromAction { mContextReference.get()?.let { InboxDatabase.getInstance(it).dao().insert(messageReported) } }
            .subscribeOn(Schedulers.io()).`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
            .subscribe({}, { })
    }

    @WorkerThread
    fun isMessageReported(messageId: Long): Boolean {
        return mContextReference.get()?.let { InboxDatabase.getInstance(it).dao().isMessageReported(messageId) } ?: false
    }

    @SuppressLint("CheckResult")
    fun deleteDatabase() {
        Completable.fromAction { mContextReference.get()?.let { InboxDatabase.getInstance(it).clearAllTables() } }
            .subscribeOn(Schedulers.io()).`as`(AutoDispose.autoDisposable<Any>(ScopeProvider.UNBOUND))
            .subscribe({}, { throwable: Throwable -> Log.e("deleteDatabase", throwable.message) })
    }
}
