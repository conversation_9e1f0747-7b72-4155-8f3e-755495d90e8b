package com.mingle.inbox.services

import android.content.Intent
import android.os.Binder
import android.os.IBinder
import androidx.annotation.WorkerThread
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleService
import com.mingle.inbox.constants.Constants
import com.mingle.inbox.interfaces.InboxGetConversationCallback
import com.mingle.inbox.interfaces.InboxManagementCallback
import com.mingle.inbox.interfaces.InboxServiceCallback
import com.mingle.inbox.management.InboxManagement
import com.mingle.inbox.management.InboxManagement.Companion.getInstance
import com.mingle.inbox.model.InboxConversation
import com.mingle.inbox.model.InboxMessage
import com.mingle.inbox.model.InboxUser
import com.uber.autodispose.AutoDispose
import com.uber.autodispose.android.lifecycle.AndroidLifecycleScopeProvider
import io.reactivex.Completable
import io.reactivex.schedulers.Schedulers

class InboxService : LifecycleService(), InboxManagementCallback {
    private val myBinder: IBinder = InboxBinder()
    private var inboxManagement: InboxManagement? = null
    private var inboxServiceCallBack: InboxServiceCallback? = null

    override fun onBind(intent: Intent): IBinder {
        super.onBind(intent)
        if (inboxManagement == null && intent.extras != null) {
            intent.extras?.let {
                val isStaging = it.getBoolean(Constants.IS_STAGING)
                val stagingAuthorizationUser = it.getString(Constants.STAGING_AUTHORIZATION_USER)
                val stagingAuthorizationPassword = it.getString(Constants.STAGING_AUTHORIZATION_PASSWORD)
                val inboxServerAddress = it.getString(Constants.INBOX_SERVER_ADDRESS) ?: throw IllegalArgumentException("Invalid Inbox Server Address")
                val inboxServerToken = it.getString(Constants.INBOX_SERVER_TOKEN) ?: throw IllegalArgumentException("Invalid Inbox Server Token")
                val inboxServerSalt = it.getString(Constants.INBOX_SERVER_SALT) ?: throw IllegalArgumentException("Invalid Inbox Server ")
                inboxManagement = getInstance(
                    applicationContext, isStaging, stagingAuthorizationUser,
                    stagingAuthorizationPassword, inboxServerAddress, inboxServerToken, inboxServerSalt, this
                )
            }
        }
        return myBinder
    }

    inner class InboxBinder : Binder() {
        val service: InboxService
            get() = this@InboxService
    }

    override fun onDestroy() {
        releaseResources()
        super.onDestroy()
    }

    fun setInboxServiceCallBack(inboxServiceCallBack: InboxServiceCallback) {
        this.inboxServiceCallBack = inboxServiceCallBack
    }

    fun initInbox(appName: String, inboxUserId: Int, deviceId: String, authToken: String, languagePreference: String, cloudProjectNumber: Long) {
        if (inboxManagement?.isInitialized != true) {
            Completable.fromAction { inboxManagement?.initInbox(this, appName, inboxUserId, deviceId, authToken, languagePreference, cloudProjectNumber) }
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.computation())
                .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
                .subscribe()
        }
    }

    fun reloadActionTokenSettings() {
        if (inboxManagement != null) {
            inboxManagement?.reloadActionTokenSettings()
        }
    }

    fun setLanguagePreference(languagePreference: String) {
        if (inboxManagement != null) {
            inboxManagement?.setLanguagePreference(languagePreference)
        }
    }

    fun resetInbox() {
        inboxManagement?.resetInbox()
    }

    fun releaseResources() {
        if (inboxManagement != null) {
            inboxManagement?.releaseResources()
        }
    }

    val numberOfUnreadConversations: Int
        get() = inboxManagement?.numberOfUnreadConversations ?: 0

    val numberOfConversationsRequest: Int
        get() = inboxManagement?.numberOfConversationsRequest ?: 0

    val numberOfUnreadConversationsRequest: Int
        get() = inboxManagement?.numberOfUnreadConversationsRequest ?: 0

    fun canCreateConversationByAd(): Boolean {
        return (inboxManagement?.remainingDirectConversationsCanCreate ?: 0) > 0
    }

    val conversations: List<InboxConversation>
        get() = inboxManagement?.conversations?.filter { !it.isConversationRequest } ?: listOf()

    val requestConversations: List<InboxConversation>
        get() = inboxManagement?.conversations?.filter { it.isConversationRequest } ?: listOf()

    //Database must be access on background thread
    @WorkerThread
    fun getLocalConversation(conversationId: Int): InboxConversation? {
        return inboxManagement?.getLocalConversation(conversationId)
    }

    fun getLocalConversationByInboxUserId(inboxUserId: Int): InboxConversation? {
        return inboxManagement?.getLocalConversationByInboxUserId(inboxUserId)
    }

    fun getFriendInboxUser(conversation: InboxConversation?): InboxUser? {
        return inboxManagement?.getFriendInboxUser(conversation)
    }

    fun updateFriendInboxUserStatus(conversation: InboxConversation?, userStatus: String?) {
        Completable.fromAction { inboxManagement?.updateFriendInboxUserStatus(conversation, userStatus) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun getMyInboxUser(conversation: InboxConversation?): InboxUser? {
        return inboxManagement?.getMyInboxUser(conversation)
    }

    fun getInboxUserIds(conversationId: Int): ArrayList<Int> {
        return inboxManagement?.getInboxUserIds(conversationId) ?: ArrayList()
    }

    @JvmOverloads
    fun getNewConversations(conversationRequest: Boolean = false) {
        Completable.fromAction { inboxManagement?.getNewConversations(conversationRequest) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    @JvmOverloads
    fun getOlderConversations(conversationRequest: Boolean = false) {
        Completable.fromAction { inboxManagement?.getOlderConversations(conversationRequest) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun getConversation(inboxUserId: Int, callback: InboxGetConversationCallback?) {
        Completable.fromAction { inboxManagement?.getConversation(inboxUserId, callback) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe({}, { throwable: Throwable? ->
                callback?.onError(throwable)
            })
    }

    fun getConversationByInboxUserId(targetInboxUserId: Int, callback: InboxGetConversationCallback) {
        Completable.fromAction { inboxManagement?.getConversationByInboxUserId(targetInboxUserId, callback) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun markConversationAsSeen(conversation: InboxConversation?) {
        Completable.fromAction { inboxManagement?.markConversationAsSeen(conversation) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun updateConversationViewedCount(conversation: InboxConversation?) {
        Completable.fromAction { inboxManagement?.updateConversationViewedCount(conversation) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun enableConversationMedia(conversation: InboxConversation?) {
        Completable.fromAction { inboxManagement?.enableConversationMedia(conversation) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun disableConversationMedia(conversation: InboxConversation?) {
        Completable.fromAction { inboxManagement?.disableConversationMedia(conversation) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun acceptConversationMediaPrompt(conversationId: Int, conversationPromptId: Int) {
        Completable.fromAction { inboxManagement?.acceptConversationMediaPrompt(conversationId, conversationPromptId) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun rejectConversationMediaPrompt(conversationId: Int, conversationPromptId: Int) {
        Completable.fromAction { inboxManagement?.rejectConversationMediaPrompt(conversationId, conversationPromptId) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun markConversationMediaPromptAsRead(conversationId: Int, conversationPromptId: Int) {
        Completable.fromAction { inboxManagement?.markConversationMediaPromptAsRead(conversationId, conversationPromptId) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun setConversationMediaPromptClosed(conversationId: Int, conversationPromptId: Int) {
        Completable.fromAction { inboxManagement?.setConversationMediaPromptClosed(conversationId, conversationPromptId) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun addConversationEncounterLikeUserId(conversation: InboxConversation?, inboxUserId: Int) {
        Completable.fromAction { inboxManagement?.addConversationEncounterLikeUserId(conversation, inboxUserId) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun deleteConversation(conversationId: Int) {
        Completable.fromAction { inboxManagement?.deleteConversation(conversationId) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun deleteLocalConversation(conversationId: Int) {
        Completable.fromAction { inboxManagement?.deleteLocalConversation(conversationId) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun saveConversation(inboxConversation: InboxConversation?) {
        Completable.fromAction { inboxManagement?.saveConversation(inboxConversation) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun updateUserProfile(conversationId: Int, listProfile: ArrayList<InboxUser>, isSystemMessage: Boolean) {
        Completable.fromAction { inboxManagement?.updateUserProfile(conversationId, listProfile, isSystemMessage) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun updateUserProfile(listConversationId: ArrayList<Int>, listProfile: ArrayList<InboxUser>) {
        Completable.fromAction { inboxManagement?.updateUserProfile(listConversationId, listProfile) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun getLatestMessages(conversationId: Int) {
        Completable.fromAction { inboxManagement?.getLatestMessages(conversationId) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun getMoreMessages(conversationId: Int) {
        Completable.fromAction { inboxManagement?.getMoreMessages(conversationId, 0, 0) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun canSendMessage(senderInboxUserId: Int, receiverInboxUserId: Int) {
        Completable.fromAction { inboxManagement?.canSendMessage(senderInboxUserId, receiverInboxUserId) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun sendMessage(conversation: InboxConversation?, message: InboxMessage?) {
        Completable.fromAction { inboxManagement?.sendMessage(conversation, message) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun sendDirectMessage(message: InboxMessage, targetInboxUserId: Int, userName: String, isDirectChatByAd: Boolean) {
        Completable.fromAction { inboxManagement?.sendDirectMessage(message, targetInboxUserId, userName, isDirectChatByAd) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun deleteMessage(conversationId: Int, message: InboxMessage?) {
        Completable.fromAction { inboxManagement?.deleteMessage(conversationId, message) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun sendReadMessage(inboxConversation: InboxConversation) {
        Completable.fromAction { inboxManagement?.sendReadMessage(inboxConversation) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun sendReadFlashMessage(inboxMessage: InboxMessage?) {
        Completable.fromAction { inboxManagement?.sendReadFlashMessage(inboxMessage) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun reportMessage(messageId: Long, category: String?, layer1Reason: String?, layer2Reason: String?, layer3Reason: String?): Completable? {
        return inboxManagement?.reportMessage(messageId, category, layer1Reason, layer2Reason, layer3Reason)
    }

    fun insertMessageReported(messageId: Long) {
        inboxManagement?.insertMessageReported(messageId)
    }

    fun updateSeenMessageIds(conversation: InboxConversation) {
        Completable.fromAction { inboxManagement?.updateSeenMessageIds(conversation) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun blockUser(friendUserId: Int, friendUserInboxId: Int) {
        Completable.fromAction { inboxManagement?.blockUser(friendUserId, friendUserInboxId) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun unblockUser(friendUserId: Int, friendUserInboxId: Int) {
        Completable.fromAction { inboxManagement?.unblockUser(friendUserId, friendUserInboxId) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    fun checkToUpdateConversationLikeStatus(friendUserInboxId: Int, isLiked: Boolean) {
        Completable.fromAction { inboxManagement?.checkToUpdateConversationLikeStatus(friendUserInboxId, isLiked) }
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .`as`(AutoDispose.autoDisposable<Any>(AndroidLifecycleScopeProvider.from(this, Lifecycle.Event.ON_DESTROY)))
            .subscribe()
    }

    /* Pusher event */
    fun solvePusherMessageCreated(data: String?) {
        inboxManagement?.solvePusherMessageCreated(data)
    }

    fun solvePusherMessageDeleted(data: String) {
        inboxManagement?.solvePusherMessageDeleted(data)
    }

    fun solvePusherConversationSeen(data: String) {
        inboxManagement?.solvePusherConversationSeen(data)
    }

    fun solvePusherRewindEncounterLike(data: String) {
        inboxManagement?.solvePusherRewindEncounterLike(data)
    }

    fun solvePusherLikeReceived(friendInboxUserId: Int) {
        inboxManagement?.solvePusherLikeReceived(friendInboxUserId)
    }

    fun solvePusherAskingMediaPermissionCreated(data: String) {
        inboxManagement?.solvePusherAskingMediaPermissionCreated(data)
    }

    fun solvePusherAskingMediaPermissionReplied(data: String) {
        inboxManagement?.solvePusherAskingMediaPermissionReplied(data)
    }

    /* End Pusher event */ /* Callback */
    override fun requestUpdateUserProfile(conversationId: Int, inboxUserIds: ArrayList<Int>, isSystemMessage: Boolean) {
        inboxServiceCallBack?.requestUpdateUserProfile(conversationId, inboxUserIds, isSystemMessage)
    }

    override fun requestUpdateUserProfile(listConversationId: ArrayList<Int>, inboxUserIds: ArrayList<Int>) {
        inboxServiceCallBack?.requestUpdateUserProfile(listConversationId, inboxUserIds)
    }

    override fun recordInboxException(exception: Exception) {
        inboxServiceCallBack?.recordInboxException(exception)
    } /* End Callback */
}
