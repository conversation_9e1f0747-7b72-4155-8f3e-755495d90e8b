package com.mingle.inbox.utils

import com.google.android.gms.common.util.CollectionUtils
import com.google.gson.GsonBuilder
import com.mingle.inbox.constants.ConversationStatus
import com.mingle.inbox.model.InboxConversation
import com.mingle.inbox.model.InboxMessage
import com.mingle.inbox.model.InboxUser
import com.mingle.inbox.model.response.CommonErrorResponse

object InboxUtils {

    fun getInboxMessage(result: String?): InboxMessage? =
        GsonBuilder().create().fromJson(result, InboxMessage::class.java)?.apply {
            user?.takeIf { it.id != 0 && it.inboxUserId == 0 }?.also {
                it.inboxUserId = it.id
                it.id = 0
            }
        }

    fun getErrorMessage(result: String?): CommonErrorResponse {
        val gson = GsonBuilder().create()
        return gson.fromJson(result, CommonErrorResponse::class.java)
    }

    @JvmStatic
    fun isUnreadConversation(conversation: InboxConversation?): <PERSON><PERSON><PERSON> {
        if (conversation?.status == null || ConversationStatus.LEFT.equals(conversation.status, ignoreCase = true)) {
            return false
        }
        return conversation.status == ConversationStatus.UNREAD
    }

    @Synchronized
    fun sortConversationsByTime(conversations: MutableList<InboxConversation>) {
        conversations.sortWith { a, b ->
            val t1 = a.lastMessageCreatedAt
            val t2 = b.lastMessageCreatedAt
            when {
                t1 == null && t2 == null -> 0 // both missing → equal
                t1 == null -> 1 // a missing → a goes “after” b
                t2 == null -> -1 // b missing → b goes “after” a
                else -> t2.compareTo(t1) // descending by timestamp
            }
        }
    }

    @Synchronized
    fun sortMessagesById(messages: ArrayList<InboxMessage>) {
        synchronized(messages) {
            messages.sortWith { lhs: InboxMessage, rhs: InboxMessage -> lhs.id.compareTo(rhs.id) }
        }
    }

    fun getConversation(conversationId: Int, conversations: List<InboxConversation>?): InboxConversation? {
        if (conversations != null) {
            synchronized(conversations) {
                for (i in conversations.indices) {
                    val inboxConversation = conversations[i]
                    if (inboxConversation.id == conversationId) {
                        return inboxConversation
                    }
                }
            }
        }
        return null
    }

    fun getConversationByInboxUserId(inboxUserId: Int, conversations: List<InboxConversation>?): InboxConversation? {
        if (conversations != null) {
            try {
                synchronized(conversations) {
                    for (conversation in conversations) {
                        for (user in conversation.users) {
                            if (user.inboxUserId == inboxUserId) {
                                return conversation
                            }
                        }
                    }
                }
            } catch (e: Throwable) {
                e.printStackTrace()
            }
        }
        return null
    }

    fun getFriendInboxUser(conversation: InboxConversation?, myInboxUserId: Int): InboxUser? {
        if (conversation != null && !CollectionUtils.isEmpty(conversation.users)) {
            for (index in conversation.users.indices) {
                if (conversation.users[index].inboxUserId != myInboxUserId) {
                    return conversation.users[index]
                }
            }
        }
        return null
    }

    fun getFriendInboxUsers(conversation: InboxConversation?, myInboxUserId: Int): ArrayList<InboxUser> {
        val friendUsers = ArrayList<InboxUser>()
        if (conversation != null && !CollectionUtils.isEmpty(conversation.users)) {
            for (index in conversation.users.indices) {
                if (conversation.users[index].inboxUserId != myInboxUserId) {
                    friendUsers.add(conversation.users[index])
                }
            }
        }
        return friendUsers
    }

    fun getMyInboxUser(conversation: InboxConversation?, myInboxUserId: Int): InboxUser? {
        var user: InboxUser? = null
        if (conversation?.users != null) {
            for (index in conversation.users.indices) {
                if (conversation.users[index].inboxUserId == myInboxUserId) {
                    user = conversation.users[index]
                    break
                }
            }
        }
        return user
    }

//    fun getFriendInboxUserIds(conversation: InboxConversation?, myInboxUserId: Int): ArrayList<Int> {
//        val friendUserInboxIds = ArrayList<Int>()
//        if (conversation?.users != null) {
//            for (index in conversation.users.indices) {
//                if (conversation.users[index].inboxUserId != myInboxUserId) {
//                    friendUserInboxIds.add(conversation.users[index].inboxUserId)
//                }
//            }
//        }
//        return friendUserInboxIds
//    }

    fun getInboxUserIds(conversation: InboxConversation?): ArrayList<Int> {
        val inboxUserIds = ArrayList<Int>()
        if (conversation != null && !CollectionUtils.isEmpty(conversation.users)) {
            for (index in conversation.users.indices) {
                inboxUserIds.add(conversation.users[index].inboxUserId)
            }
        }
        return inboxUserIds
    }

    fun <T> equals(a: T?, b: T?): Boolean {
        return (a == null && b == null) || (a != null && a == b)
    }
}
