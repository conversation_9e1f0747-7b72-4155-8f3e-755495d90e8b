package com.mingle.inbox.model.request

import com.google.gson.annotations.SerializedName

class GetLatestMessages(appName: String, inboxUserId: Int, deviceId: String, languagePreference: String) :
    BaseRequest(appName, inboxUserId, deviceId, languagePreference) {
    @SerializedName(PARAM_CONVERSATION_ID)
    private var conversationId: String? = null

    fun setConversationId(conversationId: String?) {
        this.conversationId = conversationId
    }

    override val queryMap: MutableMap<String, Any>
        get() {
            val objectMap = super.queryMap
            conversationId?.let { objectMap[PARAM_CONVERSATION_ID] = it }
            return objectMap
        }

    companion object {
        private const val PARAM_CONVERSATION_ID = "conversation_id"
    }
}
