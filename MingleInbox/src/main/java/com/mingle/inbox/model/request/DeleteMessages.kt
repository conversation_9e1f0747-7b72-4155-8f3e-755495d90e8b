package com.mingle.inbox.model.request

import com.google.gson.annotations.SerializedName

class DeleteMessages(appName: String, inboxUserId: Int, deviceId: String, languagePreference: String) :
    BaseRequest(appName, inboxUserId, deviceId, languagePreference) {
    @SerializedName(PARAM_LAST_DELETED_MESSAGE_ID)
    private var lastDeletedMessageId: String? = null

    fun setLastDeletedMessageId(lastDeletedMessageId: String?) {
        this.lastDeletedMessageId = lastDeletedMessageId
    }

    override val queryMap: MutableMap<String, Any>
        get() {
            val objectMap = super.queryMap
            lastDeletedMessageId?.let { objectMap[PARAM_LAST_DELETED_MESSAGE_ID] = it }
            return objectMap
        }

    companion object {
        private const val PARAM_LAST_DELETED_MESSAGE_ID = "last_deleted_message_id"
    }
}
