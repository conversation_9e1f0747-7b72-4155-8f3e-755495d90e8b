package com.mingle.inbox.model.eventbus.net;

import com.mingle.inbox.model.InboxMessage;

import java.util.ArrayList;

public class InboxGetMoreMessageEvent extends InboxBaseEvent {
    private int conversationId;
    private ArrayList<InboxMessage> messages;
    private boolean stopLoadMore;

    public int getConversationId() {
        return conversationId;
    }

    public void setConversationId(int conversationId) {
        this.conversationId = conversationId;
    }

    public ArrayList<InboxMessage> getMessages() {
        return messages;
    }

    public void setMessages(ArrayList<InboxMessage> messages) {
        this.messages = messages;
    }

    public boolean isStopLoadMore() {
        return stopLoadMore;
    }

    public void setStopLoadMore(boolean stopLoadMore) {
        this.stopLoadMore = stopLoadMore;
    }
}
