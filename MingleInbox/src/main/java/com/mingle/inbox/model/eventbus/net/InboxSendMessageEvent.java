package com.mingle.inbox.model.eventbus.net;

public class InboxSendMessageEvent extends InboxBaseEvent {
    private int conversationId;
    private boolean isDirectMessage;

    public int getConversationId() {
        return conversationId;
    }

    public void setConversationId(int conversationId) {
        this.conversationId = conversationId;
    }

    public void setDirectMessage(boolean isDirectMessage) {
        this.isDirectMessage = isDirectMessage;
    }

    public boolean isDirectMessage() {
        return isDirectMessage;
    }
}
