package com.mingle.inbox.model.request

import com.google.gson.annotations.SerializedName

open class BaseRequest(
    @field:SerializedName(PARAM_APP_NAME) private val appName: String,
    @field:SerializedName(PARAM_USER_ID) protected var inboxUserId: Int,
    @field:SerializedName(PARAM_USER_IDENTITY) protected var inboxUserIdentity: String,
    @field:SerializedName(PARAM_LANGUAGE_PREFERENCE) protected var languagePreference: String
) {
    @SerializedName(PARAM_SUPPORT_CONVERSATION_REQUEST)
    protected var supportConversationRequest: Boolean = true

    @SerializedName(PARAM_SUPPORT_CONVERSATION_SETTING)
    protected var supportConversationSetting: Boolean = true

    open val queryMap: MutableMap<String, Any>
        get() {
            val options: MutableMap<String, Any> = HashMap()
            options[PARAM_APP_NAME] = appName
            options[PARAM_USER_ID] = inboxUserId
            options[PARAM_USER_IDENTITY] = inboxUserIdentity
            options[PARAM_LANGUAGE_PREFERENCE] = languagePreference
            options[PARAM_SUPPORT_CONVERSATION_REQUEST] = supportConversationRequest
            options[PARAM_SUPPORT_CONVERSATION_SETTING] = supportConversationSetting
            //Check to remove this param when clients are force updated
            return options
        }

    companion object {
        private const val PARAM_APP_NAME = "app_name"
        private const val PARAM_USER_ID = "user_id"
        private const val PARAM_USER_IDENTITY = "user_identity"
        private const val PARAM_LANGUAGE_PREFERENCE = "language_preference"
        private const val PARAM_SUPPORT_CONVERSATION_REQUEST = "support_conversation_request"
        private const val PARAM_SUPPORT_CONVERSATION_SETTING = "support_conversation_setting"
    }
}
