package com.mingle.inbox.model.eventbus.net;

import com.mingle.inbox.model.InboxMessage;

public class InboxUploadAudioEvent extends InboxBaseEvent {
    private int conversationId;
    private InboxMessage inboxMessage;

    public int getConversationId() {
        return conversationId;
    }

    public void setConversationId(int conversationId) {
        this.conversationId = conversationId;
    }

    public InboxMessage getInboxMessage() {
        return inboxMessage;
    }

    public void setInboxMessage(InboxMessage inboxMessage) {
        this.inboxMessage = inboxMessage;
    }

}
