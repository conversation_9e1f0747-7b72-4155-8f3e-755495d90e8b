package com.mingle.inbox.model.eventbus.net;

public class InboxGetLatestMessageEvent extends InboxBaseEvent {
    private int conversationId;
    private boolean stopLoadMore;

    public int getConversationId() {
        return conversationId;
    }

    public void setConversationId(int conversationId) {
        this.conversationId = conversationId;
    }

    public boolean isStopLoadMore() {
        return stopLoadMore;
    }

    public void setStopLoadMore(boolean stopLoadMore) {
        this.stopLoadMore = stopLoadMore;
    }
}
