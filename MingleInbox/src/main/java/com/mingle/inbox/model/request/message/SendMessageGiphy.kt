package com.mingle.inbox.model.request.message

import com.google.gson.annotations.SerializedName
import com.mingle.inbox.model.InboxGiphyContent
import com.mingle.inbox.model.InboxMessage

class SendMessageGiphy : SendMessageBase {
    @SerializedName("giphy_content")
    private val giphyContent: InboxGiphyContent

    constructor(
        appName: String, inboxUserId: Int, deviceId: String, languagePreference: String,
        inboxUserIds: List<Int>, flashDuration: Int, giphyContent: InboxGiphyContent
    ) : super(appName, inboxUserId, deviceId, languagePreference, inboxUserIds, flashDuration, InboxMessage.MESSAGE_TYPE_GIPHY) {
        this.giphyContent = giphyContent
    }

    constructor(
        appName: String, inboxUserId: Int, deviceId: String, languagePreference: String,
        conversationId: Int, inboxUserIds: List<Int>, flashDuration: Int, giphyContent: InboxGiphyContent
    ) : super(appName, inboxUserId, deviceId, languagePreference, conversationId, inboxUserIds, flashDuration, InboxMessage.MESSAGE_TYPE_GIPHY) {
        this.giphyContent = giphyContent
    }
}
