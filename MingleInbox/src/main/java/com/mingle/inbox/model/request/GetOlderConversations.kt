package com.mingle.inbox.model.request

import com.google.gson.annotations.SerializedName

class GetOlderConversations(appName: String, inboxUserId: Int, deviceId: String, languagePreference: String) :
    BaseRequest(appName, inboxUserId, deviceId, languagePreference) {

    @SerializedName(PARAM_LAST_MESSAGE_CREATED_AT)
    var lastMessageCreatedAt: String? = null

    @SerializedName(PARAM_CONVERSATION_REQUEST)
    var conversationRequest = false

    override val queryMap: MutableMap<String, Any>
        get() {
            val objectMap = super.queryMap
            objectMap[PARAM_CONVERSATION_REQUEST] = conversationRequest
            lastMessageCreatedAt?.takeIf { it.isNotEmpty() }?.let {
                objectMap[PARAM_LAST_MESSAGE_CREATED_AT] = it
            }
            return objectMap
        }

    companion object {
        private const val PARAM_LAST_MESSAGE_CREATED_AT = "last_message_created_at"
        private const val PARAM_CONVERSATION_REQUEST = "conversation_request"
    }
}
