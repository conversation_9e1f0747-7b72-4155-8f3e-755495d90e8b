package com.mingle.inbox.model

import android.text.TextUtils
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.google.android.gms.common.util.CollectionUtils
import com.google.gson.annotations.SerializedName
import com.mingle.global.utils.ArrayUtil
import com.mingle.global.utils.date.DateTimeUtil
import com.mingle.inbox.room.converters.ArrayIntegerConverter
import com.mingle.inbox.room.converters.InboxConversationPromptListConverter
import com.mingle.inbox.room.converters.InboxListConverter
import com.mingle.inbox.room.converters.InboxMessageListConverter
import com.mingle.inbox.room.converters.InboxSeenTimeListConverter
import java.io.Serializable

@Entity(tableName = "inbox_conversation")
open class InboxConversation : Serializable {
    @JvmField
    @PrimaryKey
    var id: Int = 0

    @JvmField
    var name: String? = null

    @JvmField
    var status: String? = null

    @JvmField
    @SerializedName("conversation_source")
    var conversationSource: String? = null

    @JvmField
    @SerializedName("compatibility_description")
    var compatibilityDescription: String? = null

    @JvmField
    @TypeConverters(InboxListConverter::class)
    var users: ArrayList<InboxUser> = ArrayList()

    @JvmField
    @get:Synchronized
    @TypeConverters(InboxMessageListConverter::class)
    var messages: ArrayList<InboxMessage> = ArrayList()

    @JvmField
    @SerializedName("seen_timestamp")
    @TypeConverters(InboxSeenTimeListConverter::class)
    var seenTimestamp: ArrayList<InboxSeenTime>? = ArrayList()

    @JvmField
    @SerializedName("last_message_created_at")
    var lastMessageCreatedAt: String? = null

    @JvmField
    @SerializedName("new_messages_count")
    var newMessagesCount: Int = 0

    @JvmField
    @SerializedName("encounter_like_by_user_ids")
    @TypeConverters(ArrayIntegerConverter::class)
    var encounterLikeByUserIds: IntArray? = null

    @JvmField
    @SerializedName("is_conversation_request")
    var isConversationRequest = false

    @JvmField
    @TypeConverters(InboxConversationPromptListConverter::class)
    @SerializedName("conversation_prompts")
    var conversationPrompts: ArrayList<InboxConversationPrompt> = ArrayList()
    /* IMPORTANT: must check InboxManagement->updateConversationInfo() when ever adding new field */


    /* Local variables */
    @JvmField
    var shouldMarkConversationAsSeen = false

    fun updateUserProfile(indexedListProfile: HashMap<String?, InboxUser?>) {
        for (index in users.indices) {
            val indexedProfile = indexedListProfile[users[index].inboxUserId.toString()]
            if (indexedProfile != null) {
                indexedProfile.cacheTime = System.currentTimeMillis()
                users[index] = indexedProfile
            }
        }

        messages.let {
            for (index in it.indices) {
                val inboxMessage = it[index]
                inboxMessage.user?.let { user ->
                    val indexedProfile = indexedListProfile[user.inboxUserId.toString()]
                    if (indexedProfile != null) {
                        inboxMessage.user = indexedProfile
                    }
                }
            }
        }
    }

    fun haveInteracted(): Boolean {
        try {
            messages.let {
                for (i in it.indices) {
                    val message = it[i]
                    if (//Message was sent by user (not a system message)
                        message.user != null && (message.flashDuration <= 0 || !message.isViewingFlashMessage)) {
                        return true
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    fun getSeenAt(inboxUser: InboxUser): String {
        var seenAt = ""
        seenTimestamp?.let {
            for (i in it.indices) {
                if (it[i].inboxUserId == inboxUser.inboxUserId) {
                    seenAt = it[i].seenAt ?: ""
                    break
                }
            }
        }
        return seenAt
    }

    fun getNewViewedCount(myInboxUser: InboxUser): Int {
        var newViewedCount = 0
        val seenAt = getSeenAt(myInboxUser)
        if (!TextUtils.isEmpty(seenAt)) {
            messages.let {
                for (i in it.indices.reversed()) {
                    val inboxMessage = it[i]
                    if (DateTimeUtil.getTime(inboxMessage.createdAt, DateTimeUtil.defaultFormatExtended) > 0
                        && DateTimeUtil.getTime(inboxMessage.createdAt, DateTimeUtil.defaultFormatExtended) >= DateTimeUtil.getTime(seenAt, DateTimeUtil.defaultFormatExtended)
                    ) {
                        if (inboxMessage.user?.id != myInboxUser.id) {
                            newViewedCount += 1
                        }
                    } else {
                        break
                    }
                }
            }


        }
        return newViewedCount
    }

    val isSourceEncounterZodiac: Boolean
        get() = SOURCE_ENCOUNTER_ZODIAC.equals(conversationSource, ignoreCase = true)

    val isSourceEncounterPersonality: Boolean
        get() = SOURCE_ENCOUNTER_PERSONALITY.equals(conversationSource, ignoreCase = true)

    val isSourceEncounter: Boolean
        get() = isSourceEncounterZodiac || isSourceEncounterPersonality

    fun shouldHideFriendInfo(): Boolean {
        if (isSourceEncounter) {
            return CollectionUtils.isEmpty(users) || encounterLikeByUserIds == null || users.size > (encounterLikeByUserIds?.size ?: 0)
        }
        return false
    }

    fun isEncounterLiked(inboxUserId: Int): Boolean {
        if (encounterLikeByUserIds != null) {
            return ArrayUtil.arrayContains(encounterLikeByUserIds, inboxUserId)
        }
        return false
    }

    fun addEncounterLikeUserId(inboxUserId: Int) {
        if (encounterLikeByUserIds != null) {
            if (!ArrayUtil.arrayContains(encounterLikeByUserIds, inboxUserId)) {
                encounterLikeByUserIds = ArrayUtil.addArrayItem(encounterLikeByUserIds, inboxUserId)
            }
        } else {
            encounterLikeByUserIds = intArrayOf(inboxUserId)
        }
    }

    fun removeEncounterLikeUserId(inboxUserId: Int) {
        if (encounterLikeByUserIds != null && ArrayUtil.arrayContains(encounterLikeByUserIds, inboxUserId)) {
            encounterLikeByUserIds = ArrayUtil.removeArrayItem(encounterLikeByUserIds, inboxUserId)
        }
    }

    fun isReceivingMediaEnabled(inboxUserId: Int): Boolean {
        return users.firstOrNull { it.inboxUserId == inboxUserId }?.receivingMediaEnabled ?: false
    }

    fun setReceivingMediaEnabled(inboxUserId: Int, enabled: Boolean) {
        users.firstOrNull { it.inboxUserId == inboxUserId }?.let { it.receivingMediaEnabled = enabled }
    }

    fun setConversationPromptClosed(conversationPromptId: Int) {
        conversationPrompts.firstOrNull { it.id == conversationPromptId }?.let { it.closed = true }
    }

    fun resolveConversationRequestIfReplied(myInboxUserId: Int): Boolean {
        if (!isConversationRequest) return false
        var hasMyMessage = false
        var hasFriendMessage = false
        for (msg in messages) {
            val senderId = msg.user?.inboxUserId ?: continue
            if (senderId == myInboxUserId) {
                hasMyMessage = true
            } else {
                hasFriendMessage = true
            }
            if (hasMyMessage && hasFriendMessage) {
                isConversationRequest = false
                return true
            }
        }
        return false
    }

    override fun equals(other: Any?): Boolean {
        if (other !is InboxConversation) {
            return false
        }
        return this.id == other.id
    }

    override fun hashCode(): Int {
        var result = id
        result = 31 * result + (name?.hashCode() ?: 0)
        result = 31 * result + (status?.hashCode() ?: 0)
        result = 31 * result + (seenTimestamp?.hashCode() ?: 0)
        result = 31 * result + (lastMessageCreatedAt?.hashCode() ?: 0)
        result = 31 * result + newMessagesCount.hashCode()
        result = 31 * result + isConversationRequest.hashCode()
        return result
    }

    companion object {
        const val SOURCE_HI: String = "hi"
        const val SOURCE_LIKE: String = "like"

        //const val SOURCE_MATCHED: String = "matched"
        //const val SOURCE_DIRECT_CHAT: String = "direct_chat"
        const val SOURCE_HOT_PICK: String = "hot_pick"
        const val SOURCE_ENCOUNTER_ZODIAC: String = "encounter_zodiac"
        const val SOURCE_ENCOUNTER_PERSONALITY: String = "encounter_personality"
        //const val SOURCE_UNKNOWN: String = "unknown"
    }
}
