package com.mingle.inbox.model.eventbus.net;

public class InboxUnblockUser<PERSON>vent extends InboxBaseEvent {
    private int feedUserId;
    private int inboxUserId;

    public int getFeedUserId() {
        return feedUserId;
    }

    public void setFeedUserId(int feedUserId) {
        this.feedUserId = feedUserId;
    }

    public int getInboxUserId() {
        return inboxUserId;
    }

    public void setInboxUserId(int inboxUserId) {
        this.inboxUserId = inboxUserId;
    }
}
