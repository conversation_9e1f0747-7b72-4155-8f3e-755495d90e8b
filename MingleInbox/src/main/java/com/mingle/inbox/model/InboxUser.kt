package com.mingle.inbox.model

import com.google.gson.annotations.SerializedName
import com.mingle.inbox.utils.InboxUtils
import java.io.Serializable

class InboxUser : Serializable {
    @JvmField
    var id: Int = 0

    @Jvm<PERSON>ield
    @SerializedName("inbox_user_id")
    var inboxUserId: Int = 0

    @JvmField
    @SerializedName("name")
    var name: String? = null

    @JvmField
    @SerializedName("user_identity")
    var userIdentity: String? = null

    @JvmField
    @SerializedName("profile_photo_url")
    var profilePhotoUrl: String? = null

    @JvmField
    @SerializedName("is_matched")
    var isMatched = false

    @JvmField
    @SerializedName("receiving_media_enabled")
    var receivingMediaEnabled = false

    // Set temporary = false because now only scammers are asked for photo verification
    // Will redo the user's verification process later
    @SerializedName("is_verified")
    var isVerified: Boolean = false
        get() = false

    //local variables
    @JvmField
    @SerializedName("cache_time")
    var cacheTime: Long = 0

    @JvmField
    var userStatus: String? = null

    val isDeleted: Boolean
        get() = STATUS_DELETED.equals(userStatus, ignoreCase = true)

    val isScammed: Boolean
        get() = STATUS_SCAMMED.equals(userStatus, ignoreCase = true)

    val isSpammer: Boolean
        get() = STATUS_SPAMMER.equals(userStatus, ignoreCase = true)

    val isBanned: Boolean
        get() = STATUS_BANNED.equals(userStatus, ignoreCase = true)

    override fun equals(other: Any?): Boolean {
        if (other is InboxUser) {
            if (!InboxUtils.equals(id, other.id)) return false
            if (!InboxUtils.equals(inboxUserId, other.inboxUserId)) return false
            if (!InboxUtils.equals(name, other.name)) return false
            if (!InboxUtils.equals(userIdentity, other.userIdentity)) return false
            if (!InboxUtils.equals(profilePhotoUrl, other.profilePhotoUrl)) return false
            if (!InboxUtils.equals(isMatched, other.isMatched)) return false
            if (!InboxUtils.equals(receivingMediaEnabled, other.receivingMediaEnabled)) return false
            return InboxUtils.equals(cacheTime, other.cacheTime)
        }

        return super.equals(other)
    }

    override fun hashCode(): Int {
        var result = id
        result = 31 * result + inboxUserId
        result = 31 * result + (name?.hashCode() ?: 0)
        result = 31 * result + (userIdentity?.hashCode() ?: 0)
        result = 31 * result + (profilePhotoUrl?.hashCode() ?: 0)
        result = 31 * result + isMatched.hashCode()
        result = 31 * result + isVerified.hashCode()
        result = 31 * result + receivingMediaEnabled.hashCode()
        result = 31 * result + (userStatus?.hashCode() ?: 0)
        return result
    }

    companion object {
        const val VALID_CACHE_TIME_IN_MILLISECONDS: Long = (30 * 60 * 1000).toLong()
        private const val STATUS_DELETED = "deleted"
        private const val STATUS_SCAMMED = "scammed"
        private const val STATUS_SPAMMER = "spammer"
        private const val STATUS_BANNED = "banned"
    }
}
