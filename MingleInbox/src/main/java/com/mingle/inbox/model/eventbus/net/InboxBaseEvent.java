package com.mingle.inbox.model.eventbus.net;

public class InboxBaseEvent {
    public static final String NULL = "null";
    public static final String SUCCESS = "success";
    public static final String FAILED = "failed";

    protected String status;
    protected Throwable throwable;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Throwable getThrowable() {
        return throwable;
    }

    public void setThrowable(Throwable throwable) {
        this.throwable = throwable;
    }
}
