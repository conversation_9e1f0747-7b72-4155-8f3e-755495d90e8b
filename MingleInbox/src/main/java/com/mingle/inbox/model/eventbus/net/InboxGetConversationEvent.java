package com.mingle.inbox.model.eventbus.net;

import com.mingle.inbox.model.InboxConversation;

public class InboxGetConversationEvent extends InboxBaseEvent {
    private int targetInboxUserId;
    private InboxConversation inboxConversation;

    public int getTargetInboxUserId() {
        return targetInboxUserId;
    }

    public void setTargetInboxUserId(int targetInboxUserId) {
        this.targetInboxUserId = targetInboxUserId;
    }

    public InboxConversation getInboxConversation() {
        return inboxConversation;
    }

    public void setInboxConversation(InboxConversation inboxConversation) {
        this.inboxConversation = inboxConversation;
    }
}
