package com.mingle.inbox.model.request

import com.google.gson.annotations.SerializedName

class UpdateConversationViewedCount(appName: String, inboxUserId: Int, deviceId: String, languagePreference: String) :
    BaseRequest(appName, inboxUserId, deviceId, languagePreference) {
    @SerializedName(PARAM_NEW_VIEWED_COUNT)
    private var newViewedCount: String? = null

    fun setNewViewedCount(newViewedCount: String?) {
        this.newViewedCount = newViewedCount
    }

    override val queryMap: MutableMap<String, Any>
        get() {
            val objectMap = super.queryMap
            newViewedCount?.let { objectMap[PARAM_NEW_VIEWED_COUNT] = it }
            return objectMap
        }

    companion object {
        private const val PARAM_NEW_VIEWED_COUNT = "new_viewed_count"
    }
}
