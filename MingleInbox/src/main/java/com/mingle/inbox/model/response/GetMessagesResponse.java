package com.mingle.inbox.model.response;

import com.google.gson.annotations.SerializedName;
import com.mingle.inbox.model.InboxMessage;
import com.mingle.inbox.model.InboxSeenTime;

import java.util.ArrayList;

public class GetMessagesResponse {

    @SerializedName("messages")
    private ArrayList<InboxMessage> messages;

    @SerializedName("seen_timestamp")
    private ArrayList<InboxSeenTime> seenTimestamp;

    public ArrayList<InboxMessage> getMessages() {
        return messages;
    }

    public void setMessages(ArrayList<InboxMessage> messages) {
        this.messages = messages;
    }

    public ArrayList<InboxSeenTime> getSeenTimestamp() {
        return seenTimestamp;
    }

    public void setSeenTimestamp(ArrayList<InboxSeenTime> seenTimestamp) {
        this.seenTimestamp = seenTimestamp;
    }
}
