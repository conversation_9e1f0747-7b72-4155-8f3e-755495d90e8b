package com.mingle.inbox.model.request

import com.google.gson.annotations.SerializedName

open class GetNewConversations(appName: String, inboxUserId: Int, deviceId: String, languagePreference: String) :
    BaseRequest(appName, inboxUserId, deviceId, languagePreference) {

    @SerializedName(PARAM_LATEST_MESSAGE_ID)
    var latestMessageId: String? = null

    @SerializedName(PARAM_CONVERSATION_REQUEST)
    var conversationRequest = false

    override val queryMap: MutableMap<String, Any>
        get() {
            val objectMap = super.queryMap
            objectMap[PARAM_CONVERSATION_REQUEST] = conversationRequest
            latestMessageId?.let { objectMap[PARAM_LATEST_MESSAGE_ID] = it }
            return objectMap
        }

    companion object {
        private const val PARAM_LATEST_MESSAGE_ID = "latest_message_id"
        private const val PARAM_CONVERSATION_REQUEST = "conversation_request"
    }
}
