package com.mingle.inbox.model

import com.google.gson.annotations.SerializedName
import com.mingle.inbox.utils.InboxUtils
import java.io.Serializable

class InboxGiphyContent : Serializable {
    @JvmField
    var id: String? = null

    @JvmField
    @SerializedName("mobile_url")
    var mobileUrl: String? = null

    @JvmField
    @SerializedName("desktop_url")
    var desktopUrl: String? = null

    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (other is InboxGiphyContent) {
            if (!InboxUtils.equals(id, other.id)) return false
            if (!InboxUtils.equals(mobileUrl, other.mobileUrl)) return false
            return InboxUtils.equals(desktopUrl, other.desktopUrl)
        }
        return super.equals(other)
    }

    override fun hashCode(): Int {
        return id?.hashCode() ?: 0
    }
}
