package com.mingle.inbox.model.eventbus.net;

import com.mingle.inbox.model.InboxConversation;

public class InboxDeleteConversationEvent extends InboxBaseEvent {
    private int conversationId;
    private InboxConversation conversation;

    public int getConversationId() {
        return conversationId;
    }

    public void setConversationId(int conversationId) {
        this.conversationId = conversationId;
    }

    public InboxConversation getConversation() {
        return conversation;
    }

    public void setConversation(InboxConversation conversation) {
        this.conversation = conversation;
    }
}