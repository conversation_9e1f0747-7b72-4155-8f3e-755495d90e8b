package com.mingle.inbox.model.request.message

import com.google.gson.annotations.SerializedName
import com.mingle.inbox.model.request.BaseRequest

abstract class SendMessageBase : BaseRequest {
    @SerializedName("conversation_id")
    var conversationId: Int = 0

    @SerializedName("user_ids")
    var inboxUserIds: List<Int>

    @SerializedName("message_type")
    private val messageType: String

    @SerializedName("flash_duration")
    var flashDuration: Int

    @SerializedName("support_remind_user")
    var isSupportRemindUser: Boolean = true

    @SerializedName("support_direct_chat")
    var isSupportDirectChat: Boolean = true

    constructor(
        appName: String, inboxUserId: Int, deviceId: String, languagePreference: String,
        inboxUserIds: List<Int>, flashDuration: Int, messageType: String
    ) : super(appName, inboxUserId, deviceId, languagePreference) {
        this.inboxUserIds = inboxUserIds
        this.flashDuration = flashDuration
        this.messageType = messageType
    }

    constructor(
        appName: String, inboxUserId: Int, deviceId: String, languagePreference: String,
        conversationId: Int, inboxUserIds: List<Int>, flashDuration: Int, messageType: String
    ) : super(appName, inboxUserId, deviceId, languagePreference) {
        this.conversationId = conversationId
        this.inboxUserIds = inboxUserIds
        this.flashDuration = flashDuration
        this.messageType = messageType
    }

    companion object {
        const val DEFAULT_AUDIO_EXTENSION: String = "m4a"
        const val PARAM_FILE_TYPE: String = "filetype"
        const val PARAM_FILE_SIZE: String = "filesize"
        const val PARAM_FILE: String = "file"
    }
}
