package com.mingle.inbox.model

import com.google.gson.annotations.SerializedName
import com.mingle.inbox.utils.InboxUtils
import java.io.Serializable

class InboxPhotoInfo : Serializable {
    @JvmField
    @SerializedName("photo_url")
    var photoUrl: String? = null

    override fun equals(other: Any?): Boolean {
        if (other is InboxPhotoInfo) {
            return InboxUtils.equals(photoUrl, other.photoUrl)
        }
        return super.equals(other)
    }

    override fun hashCode(): Int {
        return photoUrl?.hashCode() ?: 0
    }
}
