package com.mingle.inbox.model.request.message

import com.google.gson.annotations.SerializedName
import com.mingle.inbox.model.request.BaseRequest

class ReportMessage(appName: String, inboxUserId: Int, deviceId: String, languagePreference: String) : BaseRequest(appName, inboxUserId, deviceId, languagePreference) {

    @SerializedName("category")
    var category: String? = null

    @SerializedName("layer1_reason")
    var layer1Reason: String? = null

    @SerializedName("layer2_reason")
    var layer2Reason: String? = null

    @SerializedName("layer3_reason")
    var layer3Reason: String? = null

    override val queryMap: MutableMap<String, Any>
        get() {
            val objectMap = super.queryMap
            category?.let { objectMap["category"] = it }
            layer1Reason?.let { objectMap["layer1_reason"] = it }
            layer2Reason?.let { objectMap["layer2_reason"] = it }
            layer3Reason?.let { objectMap["layer3_reason"] = it }
            return objectMap
        }
}