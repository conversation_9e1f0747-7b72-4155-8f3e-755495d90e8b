package com.mingle.inbox.model.request.message

import com.google.gson.annotations.SerializedName
import com.mingle.inbox.model.InboxMessage

class SendMessageText : SendMessageBase {
    @SerializedName("content")
    private val content: String

    constructor(
        appName: String, inboxUserId: Int, deviceId: String, languagePreference: String,
        inboxUserIds: List<Int>, flashDuration: Int, content: String
    ) : super(appName, inboxUserId, deviceId, languagePreference, inboxUserIds, flashDuration, InboxMessage.MESSAGE_TYPE_TEXT) {
        this.content = content
    }

    constructor(
        appName: String, inboxUserId: Int, deviceId: String, languagePreference: String,
        conversationId: Int, inboxUserIds: List<Int>, flashDuration: Int, content: String
    ) : super(appName, inboxUserId, deviceId, languagePreference, conversationId, inboxUserIds, flashDuration, InboxMessage.MESSAGE_TYPE_TEXT) {
        this.content = content
    }
}
