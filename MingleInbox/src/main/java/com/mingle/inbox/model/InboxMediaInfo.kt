package com.mingle.inbox.model

import com.google.gson.annotations.SerializedName
import com.mingle.inbox.utils.InboxUtils
import java.io.Serializable

class InboxMediaInfo : Serializable {
    @JvmField
    @SerializedName("video_url")
    var videoUrl: String? = null

    @JvmField
    @SerializedName("thumbnail_url")
    var thumbnailUrl: String? = null

    @JvmField
    @SerializedName("audio_url")
    var audioUrl: String? = null

    //local variables
    @JvmField
    var state: Int = 0
    @JvmField
    var progress: Float = 0f

    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (other is InboxMediaInfo) {
            if (!InboxUtils.equals(videoUrl, other.videoUrl)) return false
            if (!InboxUtils.equals(thumbnailUrl, other.thumbnailUrl)) return false
            if (!InboxUtils.equals(audioUrl, other.audioUrl)) return false
            if (!InboxUtils.equals(state, other.state)) return false
            return InboxUtils.equals(progress, other.progress)
        }
        return super.equals(other)
    }

    override fun hashCode(): Int {
        var result = videoUrl?.hashCode() ?: 0
        result = 31 * result + (thumbnailUrl?.hashCode() ?: 0)
        result = 31 * result + (audioUrl?.hashCode() ?: 0)
        return result
    }
}
