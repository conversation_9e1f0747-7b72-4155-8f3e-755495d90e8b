package com.mingle.inbox.model.request.message

import com.mingle.inbox.model.InboxMediaInfo
import com.mingle.inbox.model.InboxMessage

class SendMessageVideo : SendMessageBase {
    @Transient
    private val attachedVideo: InboxMediaInfo

    constructor(
        appName: String, inboxUserId: Int, deviceId: String, languagePreference: String,
        inboxUserIds: List<Int>, flashDuration: Int, attachedVideo: InboxMediaInfo
    ) : super(appName, inboxUserId, deviceId, languagePreference, inboxUserIds, flashDuration, InboxMessage.MESSAGE_TYPE_VIDEO) {
        this.attachedVideo = attachedVideo
    }

    constructor(
        appName: String, inboxUserId: Int, deviceId: String, languagePreference: String,
        conversationId: Int, inboxUserIds: List<Int>, flashDuration: Int, attachedVideo: InboxMediaInfo
    ) : super(appName, inboxUserId, deviceId, languagePreference, conversationId, inboxUserIds, flashDuration, InboxMessage.MESSAGE_TYPE_VIDEO) {
        this.attachedVideo = attachedVideo
    }
}
