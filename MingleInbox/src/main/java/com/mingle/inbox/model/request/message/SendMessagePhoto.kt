package com.mingle.inbox.model.request.message

import com.mingle.inbox.model.InboxMessage
import com.mingle.inbox.model.InboxPhotoInfo

class SendMessagePhoto : SendMessageBase {
    @Transient
    private val attachedPhoto: InboxPhotoInfo

    constructor(
        appName: String, inboxUserId: Int, deviceId: String, languagePreference: String,
        inboxUserIds: List<Int>, flashDuration: Int, attachedPhoto: InboxPhotoInfo
    ) : super(appName, inboxUserId, deviceId, languagePreference, inboxUserIds, flashDuration, InboxMessage.MESSAGE_TYPE_PHOTO) {
        this.attachedPhoto = attachedPhoto
    }

    constructor(
        appName: String, inboxUserId: Int, deviceId: String, languagePreference: String,
        conversationId: Int, inboxUserIds: List<Int>, flashDuration: Int, attachedPhoto: InboxPhotoInfo
    ) : super(appName, inboxUserId, deviceId, languagePreference, conversationId, inboxUserIds, flashDuration, InboxMessage.MESSAGE_TYPE_PHOTO) {
        this.attachedPhoto = attachedPhoto
    }
}
