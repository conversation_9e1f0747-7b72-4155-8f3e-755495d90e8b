package com.mingle.inbox.model

import android.content.Context
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import com.mingle.inbox.R
import java.io.Serializable

open class InboxConversationPrompt : Serializable {
    @JvmField
    @PrimaryKey
    var id: Int = 0

    @JvmField
    @SerializedName("user_id")
    var inboxUserId: Int = 0

    @JvmField
    @SerializedName("conversation_id")
    var conversationId: Int = 0

    @JvmField
    @SerializedName("prompt_type")
    var promptType: String? = null

    @JvmField
    @SerializedName("status")
    var status: String? = null

    @JvmField
    @SerializedName("read_at")
    var readAt: String? = null

    @JvmField
    @SerializedName("created_at")
    var createdAt: String? = null

    @JvmField
    @SerializedName("closed")
    var closed: Boolean = false

    fun getPromptText(context: Context): CharSequence {
        return when (promptType) {
            TYPE_ASKING -> context.getString(R.string.conversation_media_request_permission_message)
            TYPE_ACCEPTED -> context.getString(R.string.conversation_media_accepted_permission_message)
            else -> context.getString(R.string.conversation_media_rejected_permission_message)
        }
    }

    val isAskingMediaPermission get() = promptType == TYPE_ASKING

    override fun equals(other: Any?): Boolean {
        if (other !is InboxConversationPrompt) {
            return false
        }
        return this.id == other.id
    }

    override fun hashCode(): Int {
        var result = id
        result = 31 * result + inboxUserId.hashCode()
        result = 31 * result + conversationId.hashCode()
        result = 31 * result + (promptType?.hashCode() ?: 0)
        result = 31 * result + (status?.hashCode() ?: 0)
        result = 31 * result + (readAt?.hashCode() ?: 0)
        result = 31 * result + (createdAt?.hashCode() ?: 0)
        result = 31 * result + closed.hashCode()
        return result
    }

    companion object {
        const val TYPE_ASKING: String = "asking_media_permission"
        const val TYPE_ACCEPTED: String = "accepted_media_permission"
        const val TYPE_REJECTED: String = "rejected_media_permission"
    }
}
