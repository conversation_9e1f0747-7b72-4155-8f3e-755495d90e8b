package com.mingle.inbox.model

import com.google.gson.annotations.SerializedName
import com.mingle.inbox.utils.InboxUtils
import java.io.Serializable
import java.util.regex.Pattern

class InboxMessage : Serializable {
    @JvmField
    var id: Long = 0

    @JvmField
    @SerializedName("user_id")
    var inboxUserId: Int = 0

    @JvmField
    @SerializedName("content")
    var content: String? = null

    @JvmField
    @SerializedName("conversation_id")
    var conversationId: Int = 0

    @JvmField
    @SerializedName("created_at")
    var createdAt: String? = null

    @JvmField
    @SerializedName("attached_photo")
    var attachedPhoto: InboxPhotoInfo? = null

    @JvmField
    @SerializedName("attached_audio")
    var attachedAudio: InboxMediaInfo? = null

    @JvmField
    @SerializedName("attached_video")
    var attachedVideo: InboxMediaInfo? = null

    @SerializedName("giphy_content")
    var giphyContent: InboxGiphyContent? = null

    @JvmField
    @SerializedName("status")
    var status: String? = null

    @JvmField
    @SerializedName("message_type")
    var messageType: String? = null

    @JvmField
    @SerializedName("user")
    var user: InboxUser? = null

    @JvmField
    var photoPath: String? = null

    @JvmField
    var videoPath: String? = null

    @JvmField
    var audioPath: String? = null

    @JvmField
    var isLocal: Boolean = false

    @JvmField
    @SerializedName("flash_duration")
    var flashDuration: Int = 0

    @JvmField
    @SerializedName("viewed_at")
    var viewedAt: Long = 0

    @JvmField
    var isViewingFlashMessage: Boolean = false

    @JvmField
    var thumbnailUrl: String? = null

    @JvmField
    var youtubeId: String? = null
    var isParsedYoutubeId: Boolean = false

    @JvmField
    @SerializedName("sticker_url")
    var stickerUrl: String? = null

    @JvmField
    @SerializedName("seen_by_user_ids")
    var seenByInboxUserIds: ArrayList<Int>? = null

    @SerializedName("error_type")
    var errorType: String? = null

    //local variables
    //local variables
    @JvmField
    var isReported: Boolean = false

    @JvmField
    var isBlurred: Boolean = true

    val isNotEligibleToChat: Boolean
        get() = "not_eligible_for_contact" == errorType

    override fun equals(other: Any?): Boolean {
        if (other is InboxMessage) {
            return compareTo(other as InboxMessage?)
        }
        return super.equals(other)
    }

    fun compareTo(other: InboxMessage?): Boolean {
        if (other != null) {
            if (!InboxUtils.equals(inboxUserId, other.inboxUserId)) return false
            if (!InboxUtils.equals(id, other.id)) return false
            if (!InboxUtils.equals(content, other.content)) return false
            if (!InboxUtils.equals(conversationId, other.conversationId)) return false
            if (!InboxUtils.equals(createdAt, other.createdAt)) return false
            if (!InboxUtils.equals(attachedAudio, other.attachedAudio)) return false
            if (!InboxUtils.equals(attachedPhoto, other.attachedPhoto)) return false
            if (!InboxUtils.equals(attachedVideo, other.attachedVideo)) return false
            if (!InboxUtils.equals(giphyContent, other.giphyContent)) return false
            if (!InboxUtils.equals(status, other.status)) return false
            if (!InboxUtils.equals(photoPath, other.photoPath)) return false
            if (!InboxUtils.equals(videoPath, other.videoPath)) return false
            if (!InboxUtils.equals(audioPath, other.audioPath)) return false
            if (!InboxUtils.equals(isLocal, other.isLocal)) return false
            if (!InboxUtils.equals(flashDuration, other.flashDuration)) return false
            if (!InboxUtils.equals(viewedAt, other.viewedAt)) return false
            if (!InboxUtils.equals(isViewingFlashMessage, other.isViewingFlashMessage)) return false
            if (!InboxUtils.equals(thumbnailUrl, other.thumbnailUrl)) return false
            if (!InboxUtils.equals(youtubeId, other.youtubeId)) return false
            if (!InboxUtils.equals(isParsedYoutubeId, other.isParsedYoutubeId)) return false
            if (!InboxUtils.equals(messageType, other.messageType)) return false
            if (!InboxUtils.equals(stickerUrl, other.stickerUrl)) return false
            return InboxUtils.equals(errorType, other.errorType)
        }
        return false
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + inboxUserId
        return result
    }

    companion object {
        const val MESSAGE_TYPE_TEXT: String = "text"
        const val MESSAGE_TYPE_PHOTO: String = "photos"
        const val MESSAGE_TYPE_VIDEO: String = "video"
        const val MESSAGE_TYPE_AUDIO: String = "audio"
        const val MESSAGE_TYPE_STICKER: String = "sticker"
        const val MESSAGE_TYPE_GIPHY: String = "giphy"

        const val MINGLE_MESSAGE_TYPE_SYSTEM: Int = 0
        const val MINGLE_MESSAGE_TYPE_RIGHT: Int = 1
        const val MINGLE_MESSAGE_TYPE_LEFT: Int = 2
        const val MINGLE_MESSAGE_TYPE_EMPTY: Int = 3
        const val DRAFT_MESSAGE_ID: Int = -1
        const val SUCCESSFUL_MESSAGE: String = "successful"
        const val FAILED_MESSAGE: String = "failed"

        private fun getUserNameFromSystemMessage(rawSystemMessage: String): ArrayList<String> {
            val listUserName = ArrayList<String>()
            val pattern = Pattern.compile("\\{\\{(.*?)\\}\\}")
            val matcher = pattern.matcher(rawSystemMessage)
            while (matcher.find()) {
                matcher.group(1)?.let { listUserName.add(it) }
            }
            return listUserName
        }
    }
}
