package com.mingle.inbox.model.eventbus.net;


import com.mingle.inbox.model.response.CanSendMessageResponse;

public class InboxCanSendMessageEvent {
    private int receiverInboxUserId;
    private CanSendMessageResponse response;
    private Throwable throwable;

    public int getReceiverInboxUserId() {
        return receiverInboxUserId;
    }

    public void setReceiverInboxUserId(int receiverInboxUserId) {
        this.receiverInboxUserId = receiverInboxUserId;
    }

    public CanSendMessageResponse getResponse() {
        return response;
    }

    public void setResponse(CanSendMessageResponse response) {
        this.response = response;
    }

    public Throwable getThrowable() {
        return throwable;
    }

    public void setThrowable(Throwable throwable) {
        this.throwable = throwable;
    }
}
