package com.mingle.inbox.model.request.message

import com.mingle.inbox.model.InboxMediaInfo
import com.mingle.inbox.model.InboxMessage

class SendMessageAudio : SendMessageBase {
    @Transient
    private val attachedAudio: InboxMediaInfo

    constructor(
        appName: String, inboxUserId: Int, deviceId: String, languagePreference: String,
        inboxUserIds: List<Int>, flashDuration: Int, attachedAudio: InboxMediaInfo
    ) : super(appName, inboxUserId, deviceId, languagePreference, inboxUserIds, flashDuration, InboxMessage.MESSAGE_TYPE_AUDIO) {
        this.attachedAudio = attachedAudio
    }

    constructor(
        appName: String, inboxUserId: Int, deviceId: String, languagePreference: String,
        conversationId: Int, inboxUserIds: List<Int>, flashDuration: Int, attachedAudio: InboxMediaInfo
    ) : super(appName, inboxUserId, deviceId, languagePreference, conversationId, inboxUserIds, flashDuration, InboxMessage.MESSAGE_TYPE_AUDIO) {
        this.attachedAudio = attachedAudio
    }
}
