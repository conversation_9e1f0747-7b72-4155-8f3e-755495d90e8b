package com.mingle.inbox.model.request

import com.google.gson.annotations.SerializedName

class GetMoreMessages(appName: String, inboxUserId: Int, deviceId: String, languagePreference: String) :
    BaseRequest(appName, inboxUserId, deviceId, languagePreference) {
    @SerializedName(PARAM_CONVERSATION_ID)
    private var conversationId: String? = null

    @SerializedName(PARAM_LAST_MESSAGE_ID)
    private var lastMessageId: String? = null

    fun setConversationId(conversationId: String?) {
        this.conversationId = conversationId
    }

    fun setLastMessageId(lastMessageId: String?) {
        this.lastMessageId = lastMessageId
    }

    override val queryMap: MutableMap<String, Any>
        get() {
            val objectMap = super.queryMap
            conversationId?.let { objectMap[PARAM_CONVERSATION_ID] = it }
            lastMessageId?.let { objectMap[PARAM_LAST_MESSAGE_ID] = it }
            return objectMap
        }

    companion object {
        private const val PARAM_CONVERSATION_ID = "conversation_id"
        private const val PARAM_LAST_MESSAGE_ID = "last_message_id"
    }
}
