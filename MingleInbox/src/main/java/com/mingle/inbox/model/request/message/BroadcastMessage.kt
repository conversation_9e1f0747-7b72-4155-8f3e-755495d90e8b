package com.mingle.inbox.model.request.message

import com.google.gson.annotations.SerializedName
import com.mingle.inbox.model.request.BaseRequest

class BroadcastMessage(appName: String, inboxUserId: Int, deviceId: String, languagePreference: String) : BaseRequest(appName, inboxUserId, deviceId, languagePreference) {

    @SerializedName("sent_via_direct_chat")
    var sentDirectChatByAd: Boolean? = null

    override val queryMap: MutableMap<String, Any>
        get() {
            val objectMap = super.queryMap
            sentDirectChatByAd?.let { objectMap["sent_via_direct_chat"] = it }
            return objectMap
        }
}