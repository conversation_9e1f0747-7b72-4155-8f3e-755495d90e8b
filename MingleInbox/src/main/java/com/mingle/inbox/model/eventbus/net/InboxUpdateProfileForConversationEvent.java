package com.mingle.inbox.model.eventbus.net;

import com.mingle.inbox.model.InboxUser;
import com.mingle.inbox.model.InboxConversation;

import java.util.ArrayList;

public class InboxUpdateProfileForConversationEvent extends InboxBaseEvent {
    private ArrayList<InboxUser> listInboxUser;
    private InboxConversation conversation;

    public ArrayList<InboxUser> getListInboxUser() {
        return listInboxUser;
    }

    public void setListInboxUser(ArrayList<InboxUser> listInboxUser) {
        this.listInboxUser = listInboxUser;
    }

    public InboxConversation getConversation() {
        return conversation;
    }

    public void setConversation(InboxConversation conversation) {
        this.conversation = conversation;
    }
}
