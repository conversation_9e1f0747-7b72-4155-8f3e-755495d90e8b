<FindBugsFilter>

    <!-- Do not check auto-generated resources classes -->
    <Match>
        <Class name="~.*R\$.*" />
    </Match>

    <!-- Do not check auto-generated manifest classes -->
    <Match>
        <Class name="~.*Manifest\$.*" />
    </Match>

    <!-- Do not check auto-generated classes (<PERSON><PERSON> puts $ into class names) -->
    <Match>
        <Class name="~.*Dagger*.*" />
    </Match>

    <!-- Do not check for non-initialized fields in tests because usually we initialize them in @Before -->
    <Match>
        <Class name="~.*Test" />
        <Bug pattern="UWF_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR"
            type="UWF_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR" />
    </Match>

    <!-- Ignore UPM in lambdas from Retrolambda, <PERSON><PERSON><PERSON> does not correctly understand them -->
    <Match>
        <Bug code="UPM" />
        <Class name="~.*\$\$Lambda\$.*" />
    </Match>

    <!-- Ignore Butterknife auto-generated classes -->
    <Match>
        <Class name="~.*\$\$ViewBinder*" />
    </Match>
    <Match>
        <Class name="~.*\$\$ViewBinder\$InnerUnbinder*" />
    </Match>

</FindBugsFilter>