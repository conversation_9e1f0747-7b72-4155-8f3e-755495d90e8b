<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="Android Application Rules"
    xmlns="http://pmd.sf.net/ruleset/1.0.0"
    xsi:noNamespaceSchemaLocation="http://pmd.sf.net/ruleset_xml_schema.xsd"
    xsi:schemaLocation="http://pmd.sf.net/ruleset/1.0.0 http://pmd.sf.net/ruleset_xml_schema.xsd">

    <description>Custom ruleset for Dating applications</description>

    <exclude-pattern>.*/R.java</exclude-pattern>
    <exclude-pattern>.*/gen/.*</exclude-pattern>

    <rule ref="rulesets/java/android.xml" />
    <rule ref="rulesets/java/clone.xml" />
    <rule ref="rulesets/java/finalizers.xml" />
    <rule ref="rulesets/java/imports.xml">
        <!-- Espresso is designed this way !-->
        <exclude name="TooManyStaticImports" />
    </rule>
<!--    <rule ref="rulesets/java/logging-java.xml">-->
<!--        &lt;!&ndash; This rule wasn't working properly and given errors in every var call info &ndash;&gt;-->
<!--        <exclude name="GuardLogStatement" />-->
<!--    </rule>-->
    <rule ref="rulesets/java/braces.xml">
        <!-- We allow single line if's without braces -->
        <exclude name="IfStmtsMustUseBraces" />
    </rule>
    <rule ref="rulesets/java/strings.xml" >
        <!-- Exclude because causes problems with SQL Strings that usually require duplication -->
        <exclude name="AvoidDuplicateLiterals"/>
    </rule>
    <rule ref="rulesets/java/basic.xml" />
    <rule ref="rulesets/java/naming.xml">
        <exclude name="AbstractNaming" />
        <exclude name="LongVariable" />
        <exclude name="ShortMethodName" />
        <exclude name="ShortVariable" />
        <exclude name="ShortClassName" />
        <exclude name="VariableNamingConventions" />
    </rule>
</ruleset>