apply plugin: 'com.android.library'
apply plugin: 'checkstyle'
//apply plugin: 'findbugs'
apply plugin: 'pmd'

android {
    namespace = 'com.mingle.quality.profiler'
    compileSdk = Versions.compileSdkVersion
    buildToolsVersion = Versions.buildToolsVersion
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
}

def qualityConfigDir = "$project.rootDir/QualityProfiler/tools"
def reportsDir = "$project.buildDir/reports"

tasks.register('checkstyle', Checkstyle) {
    description 'Check code standard'
    group 'verification'
    configFile file("$qualityConfigDir/rules-checkstyle.xml")
    source fileTree("$project.rootDir/dating/src/main/java")
    include '**/*.java'
    exclude '**/gen/**'

    classpath = files()
    showViolations true
    ignoreFailures true

    reports {
        xml.required = true
        html.destination file("$reportsDir/checkstyle/checkstyle.html")
    }
}

// Required to compile source code before run this task
//tasks.register('findbugs', FindBugs) {
//    description 'Find bugs mainly design flaws, bad practices, multithreaded correctness and code vulnerabilities.'
//    group 'verification'
//    excludeFilter = file("$qualityConfigDir/rules-findbugs.xml")
//    classes = fileTree("$project.rootDir/app/build/intermediates/classes")
//    source = fileTree("$project.rootDir/app/src/main/java")
//    effort 'max'
//    reportLevel = "high"
//
//    classpath = files()
//    ignoreFailures true
//
//    reports {
//        xml.required = false
//        html.required = true
//        xml.destination file("$reportsDir/findbugs/findbugs.xml")
//        html.destination file("$reportsDir/findbugs/findbugs.html")
//    }
//}

tasks.register('pmd', Pmd) {
    description 'Identifying potential problems mainly dead code, duplicated code, cyclomatic complexity and overcomplicated expressions'
    group 'verification'
    ruleSetFiles = files("$qualityConfigDir/rules-pmd.xml")
    source = fileTree("$project.rootDir/dating/src/main/java")
    include '**/*.java'
    exclude '**/gen/**'
    ignoreFailures true

    reports {
        xml.required = true
        html.required = true
        xml.destination file("$reportsDir/pmd/pmd.xml")
        html.destination file("$reportsDir/pmd/pmd.html")
    }
}
